// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'index.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ProductionItemsRequestImpl _$$ProductionItemsRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductionItemsRequestImpl(
      projectId: json['projectId'] as String?,
    );

Map<String, dynamic> _$$ProductionItemsRequestImplToJson(
        _$ProductionItemsRequestImpl instance) =>
    <String, dynamic>{
      if (instance.projectId case final value?) 'projectId': value,
    };

_$ProductionTaskCancelRequestImpl _$$ProductionTaskCancelRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductionTaskCancelRequestImpl(
      taskId: json['taskId'] as String?,
      reason: json['reason'] as String?,
    );

Map<String, dynamic> _$$ProductionTaskCancelRequestImplToJson(
        _$ProductionTaskCancelRequestImpl instance) =>
    <String, dynamic>{
      if (instance.taskId case final value?) 'taskId': value,
      if (instance.reason case final value?) 'reason': value,
    };

_$ProductionTaskCreateRequestImpl _$$ProductionTaskCreateRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductionTaskCreateRequestImpl(
      projectId: json['projectId'] as String?,
      productId: json['productId'] as String?,
      quantityRequired: (json['quantityRequired'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$ProductionTaskCreateRequestImplToJson(
        _$ProductionTaskCreateRequestImpl instance) =>
    <String, dynamic>{
      if (instance.projectId case final value?) 'projectId': value,
      if (instance.productId case final value?) 'productId': value,
      if (instance.quantityRequired case final value?)
        'quantityRequired': value,
    };

_$ProductionTaskDetailsRequestImpl _$$ProductionTaskDetailsRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductionTaskDetailsRequestImpl(
      taskId: json['taskId'] as String?,
    );

Map<String, dynamic> _$$ProductionTaskDetailsRequestImplToJson(
        _$ProductionTaskDetailsRequestImpl instance) =>
    <String, dynamic>{
      if (instance.taskId case final value?) 'taskId': value,
    };

_$ProductionTaskUpdateStatusRequestImpl
    _$$ProductionTaskUpdateStatusRequestImplFromJson(
            Map<String, dynamic> json) =>
        _$ProductionTaskUpdateStatusRequestImpl(
          taskId: json['taskId'] as String?,
          status: $enumDecodeNullable(
              _$ProductionTaskStatusEnumMap, json['status']),
          comment: json['comment'] as String?,
        );

Map<String, dynamic> _$$ProductionTaskUpdateStatusRequestImplToJson(
        _$ProductionTaskUpdateStatusRequestImpl instance) =>
    <String, dynamic>{
      if (instance.taskId case final value?) 'taskId': value,
      if (_$ProductionTaskStatusEnumMap[instance.status] case final value?)
        'status': value,
      if (instance.comment case final value?) 'comment': value,
    };

const _$ProductionTaskStatusEnumMap = {
  ProductionTaskStatus.materialsRequested: 'materials_requested',
  ProductionTaskStatus.materialsIssued: 'materials_issued',
  ProductionTaskStatus.assemblyInProgress: 'assembly_in_progress',
  ProductionTaskStatus.readyForInspection: 'ready_for_inspection',
  ProductionTaskStatus.qcInspection: 'qc_inspection',
  ProductionTaskStatus.qcApproved: 'qc_approved',
};

_$ProductionTasksRequestImpl _$$ProductionTasksRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductionTasksRequestImpl(
      projectId: json['projectId'] as String?,
      productId: json['productId'] as String?,
      status:
          $enumDecodeNullable(_$ProductionTaskStatusEnumMap, json['status']),
      dateFrom: json['dateFrom'] == null
          ? null
          : DateTime.parse(json['dateFrom'] as String),
      dateTo: json['dateTo'] == null
          ? null
          : DateTime.parse(json['dateTo'] as String),
    );

Map<String, dynamic> _$$ProductionTasksRequestImplToJson(
        _$ProductionTasksRequestImpl instance) =>
    <String, dynamic>{
      if (instance.projectId case final value?) 'projectId': value,
      if (instance.productId case final value?) 'productId': value,
      if (_$ProductionTaskStatusEnumMap[instance.status] case final value?)
        'status': value,
      if (instance.dateFrom?.toIso8601String() case final value?)
        'dateFrom': value,
      if (instance.dateTo?.toIso8601String() case final value?) 'dateTo': value,
    };

_$ProductionItemsResponseImpl _$$ProductionItemsResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductionItemsResponseImpl(
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => ProductionItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalItems: (json['totalItems'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$ProductionItemsResponseImplToJson(
        _$ProductionItemsResponseImpl instance) =>
    <String, dynamic>{
      if (instance.items case final value?) 'items': value,
      if (instance.totalItems case final value?) 'totalItems': value,
    };

_$ProductionTaskCancelResponseImpl _$$ProductionTaskCancelResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductionTaskCancelResponseImpl(
      success: json['success'] as bool?,
      message: json['message'] as String?,
    );

Map<String, dynamic> _$$ProductionTaskCancelResponseImplToJson(
        _$ProductionTaskCancelResponseImpl instance) =>
    <String, dynamic>{
      if (instance.success case final value?) 'success': value,
      if (instance.message case final value?) 'message': value,
    };

_$ProductionTasksResponseImpl _$$ProductionTasksResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductionTasksResponseImpl(
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => ProductionTaskModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalItems: (json['totalItems'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$ProductionTasksResponseImplToJson(
        _$ProductionTasksResponseImpl instance) =>
    <String, dynamic>{
      if (instance.items case final value?) 'items': value,
      if (instance.totalItems case final value?) 'totalItems': value,
    };

_$ProductionItemModelImpl _$$ProductionItemModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductionItemModelImpl(
      productId: json['productId'] as String?,
      product: json['product'] == null
          ? null
          : ProductModel.fromJson(json['product'] as Map<String, dynamic>),
      requiredQuantity: (json['requiredQuantity'] as num?)?.toDouble(),
      readyQuantity: (json['readyQuantity'] as num?)?.toDouble(),
      status: $enumDecodeNullable(_$ProductionStatusEnumMap, json['status']),
      filledPercentage: (json['filledPercentage'] as num?)?.toDouble(),
      materials: (json['materials'] as List<dynamic>?)
          ?.map((e) =>
              ProductionMaterialModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      productionTask: json['productionTask'] == null
          ? null
          : ProductionTaskModel.fromJson(
              json['productionTask'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$ProductionItemModelImplToJson(
        _$ProductionItemModelImpl instance) =>
    <String, dynamic>{
      if (instance.productId case final value?) 'productId': value,
      if (instance.product case final value?) 'product': value,
      if (instance.requiredQuantity case final value?)
        'requiredQuantity': value,
      if (instance.readyQuantity case final value?) 'readyQuantity': value,
      if (_$ProductionStatusEnumMap[instance.status] case final value?)
        'status': value,
      if (instance.filledPercentage case final value?)
        'filledPercentage': value,
      if (instance.materials case final value?) 'materials': value,
      if (instance.productionTask case final value?) 'productionTask': value,
    };

const _$ProductionStatusEnumMap = {
  ProductionStatus.ready: 'ready',
  ProductionStatus.partial: 'partial',
  ProductionStatus.notReady: 'not_ready',
  ProductionStatus.pendingSubAssemblies: 'pending_sub_assemblies',
};

_$ProductionMaterialModelImpl _$$ProductionMaterialModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductionMaterialModelImpl(
      materialId: json['materialId'] as String?,
      material: json['material'] == null
          ? null
          : NomenclatureModel.fromJson(
              json['material'] as Map<String, dynamic>),
      materialRequirements: json['materialRequirements'] as String?,
      required: (json['required'] as num?)?.toDouble(),
      available: (json['available'] as num?)?.toDouble(),
      filledPercentage: (json['filledPercentage'] as num?)?.toDouble(),
      storageItem: json['storageItem'] == null
          ? null
          : MaterialModel.fromJson(json['storageItem'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$ProductionMaterialModelImplToJson(
        _$ProductionMaterialModelImpl instance) =>
    <String, dynamic>{
      if (instance.materialId case final value?) 'materialId': value,
      if (instance.material case final value?) 'material': value,
      if (instance.materialRequirements case final value?)
        'materialRequirements': value,
      if (instance.required case final value?) 'required': value,
      if (instance.available case final value?) 'available': value,
      if (instance.filledPercentage case final value?)
        'filledPercentage': value,
      if (instance.storageItem case final value?) 'storageItem': value,
    };

_$ProductionTaskModelImpl _$$ProductionTaskModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductionTaskModelImpl(
      id: json['_id'] as String?,
      projectId: json['projectId'] as String?,
      productId: json['productId'] as String?,
      status:
          $enumDecodeNullable(_$ProductionTaskStatusEnumMap, json['status']),
      quantityRequired: (json['quantityRequired'] as num?)?.toDouble(),
      materials: (json['materials'] as List<dynamic>?)
          ?.map((e) =>
              ProductionTaskMaterialModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      statusHistory: (json['statusHistory'] as List<dynamic>?)
          ?.map((e) => ProductionTaskStatusHistoryModel.fromJson(
              e as Map<String, dynamic>))
          .toList(),
      requestedBy: json['requestedBy'] as String?,
      issuedBy: json['issuedBy'] as String?,
      assembledBy: json['assembledBy'] as String?,
      inspectedBy: json['inspectedBy'] as String?,
      cancelled: json['cancelled'] as bool?,
      cancelledAt: json['cancelledAt'] == null
          ? null
          : DateTime.parse(json['cancelledAt'] as String),
      cancelledBy: json['cancelledBy'] as String?,
      cancelReason: json['cancelReason'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$ProductionTaskModelImplToJson(
        _$ProductionTaskModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) '_id': value,
      if (instance.projectId case final value?) 'projectId': value,
      if (instance.productId case final value?) 'productId': value,
      if (_$ProductionTaskStatusEnumMap[instance.status] case final value?)
        'status': value,
      if (instance.quantityRequired case final value?)
        'quantityRequired': value,
      if (instance.materials case final value?) 'materials': value,
      if (instance.statusHistory case final value?) 'statusHistory': value,
      if (instance.requestedBy case final value?) 'requestedBy': value,
      if (instance.issuedBy case final value?) 'issuedBy': value,
      if (instance.assembledBy case final value?) 'assembledBy': value,
      if (instance.inspectedBy case final value?) 'inspectedBy': value,
      if (instance.cancelled case final value?) 'cancelled': value,
      if (instance.cancelledAt?.toIso8601String() case final value?)
        'cancelledAt': value,
      if (instance.cancelledBy case final value?) 'cancelledBy': value,
      if (instance.cancelReason case final value?) 'cancelReason': value,
      if (instance.createdAt?.toIso8601String() case final value?)
        'createdAt': value,
      if (instance.updatedAt?.toIso8601String() case final value?)
        'updatedAt': value,
    };

_$ProductionTaskMaterialModelImpl _$$ProductionTaskMaterialModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductionTaskMaterialModelImpl(
      storageItemId: json['storageItemId'] as String?,
      storageItem: json['storageItem'] == null
          ? null
          : MaterialModel.fromJson(json['storageItem'] as Map<String, dynamic>),
      requiredQuantity: (json['requiredQuantity'] as num?)?.toDouble(),
      issuedQuantity: (json['issuedQuantity'] as num?)?.toDouble(),
      materialName: json['materialName'] as String?,
      materialUnit: json['materialUnit'] as String?,
    );

Map<String, dynamic> _$$ProductionTaskMaterialModelImplToJson(
        _$ProductionTaskMaterialModelImpl instance) =>
    <String, dynamic>{
      if (instance.storageItemId case final value?) 'storageItemId': value,
      if (instance.storageItem case final value?) 'storageItem': value,
      if (instance.requiredQuantity case final value?)
        'requiredQuantity': value,
      if (instance.issuedQuantity case final value?) 'issuedQuantity': value,
      if (instance.materialName case final value?) 'materialName': value,
      if (instance.materialUnit case final value?) 'materialUnit': value,
    };

_$ProductionTaskStatusHistoryModelImpl
    _$$ProductionTaskStatusHistoryModelImplFromJson(
            Map<String, dynamic> json) =>
        _$ProductionTaskStatusHistoryModelImpl(
          status: $enumDecodeNullable(
              _$ProductionTaskStatusEnumMap, json['status']),
          date: json['date'] == null
              ? null
              : DateTime.parse(json['date'] as String),
          userId: json['userId'] as String?,
          comment: json['comment'] as String?,
        );

Map<String, dynamic> _$$ProductionTaskStatusHistoryModelImplToJson(
        _$ProductionTaskStatusHistoryModelImpl instance) =>
    <String, dynamic>{
      if (_$ProductionTaskStatusEnumMap[instance.status] case final value?)
        'status': value,
      if (instance.date?.toIso8601String() case final value?) 'date': value,
      if (instance.userId case final value?) 'userId': value,
      if (instance.comment case final value?) 'comment': value,
    };
