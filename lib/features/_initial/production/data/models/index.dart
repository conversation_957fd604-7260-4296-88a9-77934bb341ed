// POST
// /production/items
// Получение списка элементов производства для проекта


// Возвращает отсортированный по приоритету список сборок с информацией о материалах и их доступности

// Parameters
// Try it out
// No parameters

// Request body

// application/json
// Example Value
// Schema
// {
//   "projectId": "string"
// }
// Responses
// Code	Description	Links
// 200	
// Успешный ответ

// Media type

// application/json
// Controls Accept header.
// Example Value
// Schema
// {
//   "items": [
//     {
//       "productId": "string",
//       "product": {
//         "_id": "string",
//         "name": "string",
//         "description": "string",
//         "number": "string",
//         "projectId": "string",
//         "parentId": "string",
//         "hasChildren": true,
//         "type": "assembly",
//         "priority": 0,
//         "isManualPriority": true,
//         "parameters": {
//           "mass": 0,
//           "width": 0,
//           "length": 0,
//           "height": 0,
//           "quantity": 0,
//           "materialId": "string",
//           "materialFeatureType": "COOPERATION",
//           "featureTypes": [
//             "CUTTING"
//           ],
//           "itemRequirements": "string",
//           "materialRequirements": "string",
//           "needMaterial": true,
//           "unitType": "kg",
//           "pureMass": 0,
//           "surfaceCoating": "string",
//           "note": "string",
//           "releaseDate": "2025-07-28T08:44:53.578Z",
//           "editedCooperationQuantity": 0,
//           "replacementMaterial": {
//             "materialId": "1cbbbb17324976ffc49b7c8a",
//             "quantity": 0,
//             "unit": "kg",
//             "mass": 0,
//             "notes": "string"
//           }
//         },
//         "department": "ogk",
//         "operations": [
//           "string"
//         ],
//         "bluePrintId": "string",
//         "imported": true,
//         "lotIds": [
//           "string"
//         ],
//         "createdAt": "2025-07-28T08:44:53.578Z",
//         "updatedAt": "2025-07-28T08:44:53.578Z"
//       },
//       "requiredQuantity": 0,
//       "readyQuantity": 0,
//       "status": "ready",
//       "filledPercentage": 0,
//       "materials": [
//         {
//           "materialId": "string",
//           "material": {
//             "_id": "615f1b5e8f6a9b1f8c8b4569",
//             "name": "Н500 ПГ",
//             "altNames": [
//               "Н500 ПГ"
//             ],
//             "visible": true,
//             "baseUnit": "kg",
//             "alternativeUnits": [
//               {
//                 "unit": "kg",
//                 "ratio": 1000
//               }
//             ],
//             "createdAt": "2025-07-28T08:44:53.578Z",
//             "updatedAt": "2025-07-28T08:44:53.578Z",
//             "materialType": "materials"
//           },
//           "materialRequirements": "string",
//           "required": 0,
//           "available": 0,
//           "filledPercentage": 0,
//           "storageItem": {
//             "_id": "615f1b5e8f6a9b1f8c8b4569",
//             "storageType": "common",
//             "materialId": "string",
//             "projectId": "string",
//             "productId": "string",
//             "itemType": "material",
//             "quantities": {
//               "ready": 5,
//               "in_production": 2,
//               "sent_to_cooperation": 0,
//               "in_cooperation": 0
//             },
//             "unit": "kg",
//             "materialRequirements": "string",
//             "comment": "string",
//             "sourceProductId": "string",
//             "processedFeatures": [
//               {
//                 "type": "CUTTING",
//                 "index": 0
//               }
//             ],
//             "isPartiallyProcessed": true,
//             "cooperationPurpose": true,
//             "targetProductId": "string",
//             "isReplacementMaterial": true,
//             "isReadyForProduction": true,
//             "productionReadiness": {
//               "hasAllFeatures": true,
//               "missingFeatures": [
//                 "CUTTING"
//               ],
//               "readyQuantity": 0,
//               "totalRequired": 0
//             },
//             "createdAt": "2025-07-28T08:44:53.578Z",
//             "updatedAt": "2025-07-28T08:44:53.578Z"
//           }
//         }
//       ],
//       "productionTask": {
//         "_id": "string",
//         "projectId": "string",
//         "productId": "string",
//         "status": "materials_requested",
//         "quantityRequired": 0,
//         "materials": [
//           {
//             "storageItemId": "string",
//             "storageItem": {
//               "_id": "615f1b5e8f6a9b1f8c8b4569",
//               "storageType": "common",
//               "materialId": "string",
//               "projectId": "string",
//               "productId": "string",
//               "itemType": "material",
//               "quantities": {
//                 "ready": 5,
//                 "in_production": 2,
//                 "sent_to_cooperation": 0,
//                 "in_cooperation": 0
//               },
//               "unit": "kg",
//               "materialRequirements": "string",
//               "comment": "string",
//               "sourceProductId": "string",
//               "processedFeatures": [
//                 {
//                   "type": "CUTTING",
//                   "index": 0
//                 }
//               ],
//               "isPartiallyProcessed": true,
//               "cooperationPurpose": true,
//               "targetProductId": "string",
//               "isReplacementMaterial": true,
//               "isReadyForProduction": true,
//               "productionReadiness": {
//                 "hasAllFeatures": true,
//                 "missingFeatures": [
//                   "CUTTING"
//                 ],
//                 "readyQuantity": 0,
//                 "totalRequired": 0
//               },
//               "createdAt": "2025-07-28T08:44:53.578Z",
//               "updatedAt": "2025-07-28T08:44:53.578Z"
//             },
//             "requiredQuantity": 0,
//             "issuedQuantity": 0,
//             "materialName": "string",
//             "materialUnit": "string"
//           }
//         ],
//         "statusHistory": [
//           {
//             "status": "materials_requested",
//             "date": "2025-07-28T08:44:53.578Z",
//             "userId": "string",
//             "comment": "string"
//           }
//         ],
//         "requestedBy": "string",
//         "issuedBy": "string",
//         "assembledBy": "string",
//         "inspectedBy": "string",
//         "cancelled": true,
//         "cancelledAt": "2025-07-28T08:44:53.578Z",
//         "cancelledBy": "string",
//         "cancelReason": "string",
//         "createdAt": "2025-07-28T08:44:53.578Z",
//         "updatedAt": "2025-07-28T08:44:53.578Z"
//       }
//     }
//   ],
//   "totalItems": 0
// }
// No links

// POST
// /production/task/cancel
// Отмена производственного задания


// Отменяет производственное задание (только если материалы не выданы)

// Parameters
// Try it out
// No parameters

// Request body

// application/json
// Example Value
// Schema
// {
//   "taskId": "string",
//   "reason": "string"
// }
// Responses
// Code	Description	Links
// 200	
// Результат отмены

// Media type

// application/json
// Controls Accept header.
// Example Value
// Schema
// {
//   "success": true,
//   "message": "string"
// }
// No links

// POST
// /production/task/create
// Создание нового производственного задания


// Создает новое производственное задание для сборки

// Parameters
// Try it out
// No parameters

// Request body

// application/json
// Example Value
// Schema
// {
//   "projectId": "string",
//   "productId": "string",
//   "quantityRequired": 0
// }
// Responses
// Code	Description	Links
// 200	
// Успешный ответ

// Media type

// application/json
// Controls Accept header.
// Example Value
// Schema
// {}
// No links

// POST
// /production/task/details
// Получение детальной информации о производственном задании


// Возвращает детальную информацию о производственном задании

// Parameters
// Try it out
// No parameters

// Request body

// application/json
// Example Value
// Schema
// {
//   "taskId": "string"
// }
// Responses
// Code	Description	Links
// 200	
// Успешный ответ

// Media type

// application/json
// Controls Accept header.
// Example Value
// Schema
// {}
// No links

// POST
// /production/task/update-status
// Обновление статуса производственного задания


// Обновляет статус производственного задания

// Parameters
// Try it out
// No parameters

// Request body

// application/json
// Example Value
// Schema
// {
//   "taskId": "string",
//   "status": "materials_requested",
//   "comment": "string"
// }
// Responses
// Code	Description	Links
// 200	
// Успешный ответ

// Media type

// application/json
// Controls Accept header.
// Example Value
// Schema
// {}
// No links

// POST
// /production/tasks
// Получение списка производственных заданий


// Возвращает список производственных заданий с возможностью фильтрации

// Parameters
// Try it out
// No parameters

// Request body

// application/json
// Example Value
// Schema
// {
//   "projectId": "string",
//   "productId": "string",
//   "status": "materials_requested",
//   "dateFrom": "2025-07-28T08:44:53.591Z",
//   "dateTo": "2025-07-28T08:44:53.591Z"
// }
// Responses
// Code	Description	Links
// 200	
// Успешный ответ

// Media type

// application/json
// Controls Accept header.
// Example Value
// Schema
// {
//   "items": [
//     {}
//   ],
//   "totalItems": 0
// }
// No links

