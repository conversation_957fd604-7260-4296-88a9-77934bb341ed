import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:sphere/features/_initial/production/data/models/index.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';

class ProductionItemsView extends StatelessWidget {
  final List<ProductionItemModel> items;
  final bool isLoading;
  final String? error;
  final Function(String productId, double quantity) onCreateTask;
  final Function(ProductionTaskModel task) onUpdateStatus;

  const ProductionItemsView({
    super.key,
    required this.items,
    required this.isLoading,
    this.error,
    required this.onCreateTask,
    required this.onUpdateStatus,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Ошибка загрузки',
              style: Fonts.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              error!,
              style: Fonts.bodyMedium.copyWith(color: AppColors.lightError),
            ),
          ],
        ),
      );
    }

    if (items.isEmpty) {
      return const Center(
        child: Text(
          'Нет элементов производства',
          style: TextStyle(fontSize: 16),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: DataTable2(
        minWidth: 1200,
        dividerThickness: 1.0,
        showCheckboxColumn: false,
        showBottomBorder: true,
        columnSpacing: 12,
        columns: const [
          DataColumn2(
            label: Text('Продукт'),
            size: ColumnSize.L,
          ),
          DataColumn2(
            label: Text('Статус'),
            size: ColumnSize.S,
          ),
          DataColumn2(
            label: Text('Требуется'),
            size: ColumnSize.S,
          ),
          DataColumn2(
            label: Text('Готово'),
            size: ColumnSize.S,
          ),
          DataColumn2(
            label: Text('Заполнено'),
            size: ColumnSize.S,
          ),
          DataColumn2(
            label: Text('Задание'),
            size: ColumnSize.M,
          ),
          DataColumn2(
            label: Text('Действия'),
            size: ColumnSize.M,
          ),
        ],
        rows: items.map((item) => _buildDataRow(context, item)).toList(),
      ),
    );
  }

  DataRow2 _buildDataRow(BuildContext context, ProductionItemModel item) {
    return DataRow2(
      cells: [
        DataCell(
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                item.product?.name ?? 'Без названия',
                style: Fonts.labelMedium,
              ),
              if (item.product?.number != null) ...[
                const SizedBox(height: 2),
                Text(
                  item.product!.number!,
                  style: Fonts.bodySmall.copyWith(
                    color: Theme.of(context).textTheme.bodySmall?.color,
                  ),
                ),
              ],
            ],
          ),
        ),
        DataCell(
          _buildStatusChip(item.status),
        ),
        DataCell(
          Text(
            item.requiredQuantity?.toStringAsFixed(2) ?? '0',
            style: Fonts.bodyMedium,
          ),
        ),
        DataCell(
          Text(
            item.readyQuantity?.toStringAsFixed(2) ?? '0',
            style: Fonts.bodyMedium,
          ),
        ),
        DataCell(
          Text(
            '${(item.filledPercentage ?? 0).toStringAsFixed(1)}%',
            style: Fonts.bodyMedium,
          ),
        ),
        DataCell(
          item.productionTask != null
              ? _buildTaskInfo(context, item.productionTask!)
              : const Text('Нет задания'),
        ),
        DataCell(
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (item.productionTask == null) ...[
                CustomElevatedButton(
                  onPressed: () => _showCreateTaskDialog(context, item),
                  text: 'Создать',
                  style: Fonts.labelSmall,
                ),
              ] else ...[
                CustomElevatedButton(
                  onPressed: () => onUpdateStatus(item.productionTask!),
                  text: 'Статус',
                  style: Fonts.labelSmall,
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatusChip(ProductionStatus? status) {
    if (status == null) return const Text('—');

    Color color;
    switch (status) {
      case ProductionStatus.ready:
        color = AppColors.lightSuccess;
        break;
      case ProductionStatus.partial:
        color = AppColors.lightWarning;
        break;
      case ProductionStatus.notReady:
        color = AppColors.lightError;
        break;
      case ProductionStatus.pendingSubAssemblies:
        color = AppColors.lightPurple;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        status.getName(),
        style: Fonts.labelSmall.copyWith(color: Colors.white),
      ),
    );
  }

  Widget _buildTaskInfo(BuildContext context, ProductionTaskModel task) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          decoration: BoxDecoration(
            color: _getTaskStatusColor(task.status),
            borderRadius: BorderRadius.circular(3),
          ),
          child: Text(
            task.status?.getName() ?? 'Неизвестно',
            style: Fonts.labelSmall.copyWith(
              color: Colors.white,
              fontSize: 10,
            ),
          ),
        ),
        if (task.quantityRequired != null) ...[
          const SizedBox(height: 2),
          Text(
            'Кол-во: ${task.quantityRequired!.toStringAsFixed(2)}',
            style: Fonts.bodySmall,
          ),
        ],
      ],
    );
  }

  Color _getTaskStatusColor(ProductionTaskStatus? status) {
    if (status == null) return Colors.grey;

    switch (status) {
      case ProductionTaskStatus.materialsRequested:
        return AppColors.lightWarning;
      case ProductionTaskStatus.materialsIssued:
        return AppColors.lightPurple;
      case ProductionTaskStatus.assemblyInProgress:
        return AppColors.lightPrimary;
      case ProductionTaskStatus.readyForInspection:
        return AppColors.lightPurple;
      case ProductionTaskStatus.qcInspection:
        return AppColors.lightWarning;
      case ProductionTaskStatus.qcApproved:
        return AppColors.lightSuccess;
    }
  }

  void _showCreateTaskDialog(BuildContext context, ProductionItemModel item) {
    final quantityController = TextEditingController(
      text: item.requiredQuantity?.toString() ?? '1',
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Создать производственное задание'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Продукт: ${item.product?.name ?? 'Без названия'}'),
            const SizedBox(height: 16),
            TextField(
              controller: quantityController,
              decoration: const InputDecoration(
                labelText: 'Количество',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Отмена'),
          ),
          ElevatedButton(
            onPressed: () {
              final quantity = double.tryParse(quantityController.text) ?? 1.0;
              onCreateTask(item.productId ?? '', quantity);
              Navigator.of(context).pop();
            },
            child: const Text('Создать'),
          ),
        ],
      ),
    );
  }
}
