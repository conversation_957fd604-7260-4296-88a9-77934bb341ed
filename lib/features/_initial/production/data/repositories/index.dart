import 'package:dio/dio.dart';
import 'package:sphere/features/_initial/production/data/models/index.dart';
import 'package:sphere/shared/data/datasources/api.dart';

class ProductionRepository {
  /// Получение списка элементов производства для проекта
  static Future<Response<ProductionItemsResponse>?> getItems(
    ProductionItemsRequest data,
  ) async {
    final body = data.toJson();

    final request = await API.request<ProductionItemsResponse>(
      url: '/production/items',
      body: body,
      method: 'POST',
      fromJson: ProductionItemsResponse.fromJson,
    );

    return request;
  }

  /// Отмена производственного задания
  static Future<Response<ProductionTaskCancelResponse>?> cancelTask(
    ProductionTaskCancelRequest data,
  ) async {
    final body = data.toJson();

    final request = await API.request<ProductionTaskCancelResponse>(
      url: '/production/task/cancel',
      body: body,
      method: 'POST',
      fromJson: ProductionTaskCancelResponse.fromJson,
    );

    return request;
  }

  /// Создание нового производственного задания
  static Future<Response<void>?> createTask(
    ProductionTaskCreateRequest data,
  ) async {
    final body = data.toJson();

    final request = await API.request<void>(
      url: '/production/task/create',
      body: body,
      method: 'POST',
    );

    return request;
  }

  /// Получение детальной информации о производственном задании
  static Future<Response<ProductionTaskModel>?> getTaskDetails(
    ProductionTaskDetailsRequest data,
  ) async {
    final body = data.toJson();

    final request = await API.request<ProductionTaskModel>(
      url: '/production/task/details',
      body: body,
      method: 'POST',
      fromJson: ProductionTaskModel.fromJson,
    );

    return request;
  }

  /// Обновление статуса производственного задания
  static Future<Response<void>?> updateTaskStatus(
    ProductionTaskUpdateStatusRequest data,
  ) async {
    final body = data.toJson();

    final request = await API.request<void>(
      url: '/production/task/update-status',
      body: body,
      method: 'POST',
    );

    return request;
  }

  /// Получение списка производственных заданий
  static Future<Response<ProductionTasksResponse>?> getTasks(
    ProductionTasksRequest data,
  ) async {
    final body = data.toJson();

    final request = await API.request<ProductionTasksResponse>(
      url: '/production/tasks',
      body: body,
      method: 'POST',
      fromJson: ProductionTasksResponse.fromJson,
    );

    return request;
  }
}
