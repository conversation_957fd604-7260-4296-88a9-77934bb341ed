// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_route/auto_route.dart' as _i24;
import 'package:flutter/foundation.dart' as _i26;
import 'package:flutter/material.dart' as _i25;
import 'package:sphere/features/_initial/branch/presentation/index.dart' as _i3;
import 'package:sphere/features/_initial/branches/presentation/index.dart'
    as _i4;
import 'package:sphere/features/_initial/index.dart' as _i7;
import 'package:sphere/features/_initial/nomenclatures/presentation/index.dart'
    as _i9;
import 'package:sphere/features/_initial/notifications/presentation/index.dart'
    as _i10;
import 'package:sphere/features/_initial/otk/presentation/defect_acts.dart'
    as _i11;
import 'package:sphere/features/_initial/otk/presentation/deliveries.dart'
    as _i12;
import 'package:sphere/features/_initial/production/presentation/index.dart'
    as _i15;
import 'package:sphere/features/_initial/settings/presentation/admin.dart'
    as _i1;
import 'package:sphere/features/_initial/settings/presentation/index.dart'
    as _i18;
import 'package:sphere/features/_initial/storage/presentation/index.dart'
    as _i20;
import 'package:sphere/features/auth/presentation/index.dart' as _i2;
import 'package:sphere/features/auth/presentation/splash.dart' as _i19;
import 'package:sphere/features/deliveries/presentation/index.dart' as _i5;
import 'package:sphere/features/files/presentation/index.dart' as _i6;
import 'package:sphere/features/product/presentation/index.dart' as _i14;
import 'package:sphere/features/project/presentation/index.dart' as _i16;
import 'package:sphere/features/project/presentation/storage.dart' as _i8;
import 'package:sphere/features/purchase_list/presentation/index.dart' as _i17;
import 'package:sphere/features/structure/presentation/index.dart' as _i21;
import 'package:sphere/features/structure/presentation/pdf_viewer.dart' as _i13;
import 'package:sphere/features/warehouses/presentation/index.dart' as _i23;
import 'package:sphere/features/warehouses/presentation/warehouse_contents.dart'
    as _i22;

/// generated route for
/// [_i1.AdminScreen]
class AdminRoute extends _i24.PageRouteInfo<void> {
  const AdminRoute({List<_i24.PageRouteInfo>? children})
      : super(
          AdminRoute.name,
          initialChildren: children,
        );

  static const String name = 'AdminRoute';

  static _i24.PageInfo page = _i24.PageInfo(
    name,
    builder: (data) {
      return const _i1.AdminScreen();
    },
  );
}

/// generated route for
/// [_i2.AuthScreen]
class AuthRoute extends _i24.PageRouteInfo<void> {
  const AuthRoute({List<_i24.PageRouteInfo>? children})
      : super(
          AuthRoute.name,
          initialChildren: children,
        );

  static const String name = 'AuthRoute';

  static _i24.PageInfo page = _i24.PageInfo(
    name,
    builder: (data) {
      return const _i2.AuthScreen();
    },
  );
}

/// generated route for
/// [_i3.BranchScreen]
class BranchRoute extends _i24.PageRouteInfo<void> {
  const BranchRoute({List<_i24.PageRouteInfo>? children})
      : super(
          BranchRoute.name,
          initialChildren: children,
        );

  static const String name = 'BranchRoute';

  static _i24.PageInfo page = _i24.PageInfo(
    name,
    builder: (data) {
      return const _i3.BranchScreen();
    },
  );
}

/// generated route for
/// [_i4.BranchesScreen]
class BranchesRoute extends _i24.PageRouteInfo<void> {
  const BranchesRoute({List<_i24.PageRouteInfo>? children})
      : super(
          BranchesRoute.name,
          initialChildren: children,
        );

  static const String name = 'BranchesRoute';

  static _i24.PageInfo page = _i24.PageInfo(
    name,
    builder: (data) {
      return const _i4.BranchesScreen();
    },
  );
}

/// generated route for
/// [_i5.DeliveriesScreen]
class DeliveriesRoute extends _i24.PageRouteInfo<DeliveriesRouteArgs> {
  DeliveriesRoute({
    _i25.Key? key,
    String? id,
    String? projectName,
    List<_i24.PageRouteInfo>? children,
  }) : super(
          DeliveriesRoute.name,
          args: DeliveriesRouteArgs(
            key: key,
            id: id,
            projectName: projectName,
          ),
          rawPathParams: {'id': id},
          initialChildren: children,
        );

  static const String name = 'DeliveriesRoute';

  static _i24.PageInfo page = _i24.PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<DeliveriesRouteArgs>(
          orElse: () => DeliveriesRouteArgs(id: pathParams.optString('id')));
      return _i5.DeliveriesScreen(
        key: args.key,
        id: args.id,
        projectName: args.projectName,
      );
    },
  );
}

class DeliveriesRouteArgs {
  const DeliveriesRouteArgs({
    this.key,
    this.id,
    this.projectName,
  });

  final _i25.Key? key;

  final String? id;

  final String? projectName;

  @override
  String toString() {
    return 'DeliveriesRouteArgs{key: $key, id: $id, projectName: $projectName}';
  }
}

/// generated route for
/// [_i6.FilesScreen]
class FilesRoute extends _i24.PageRouteInfo<FilesRouteArgs> {
  FilesRoute({
    _i25.Key? key,
    String? id,
    String? idType,
    bool? isRoot,
    String? department,
    String? projectName,
    List<_i24.PageRouteInfo>? children,
  }) : super(
          FilesRoute.name,
          args: FilesRouteArgs(
            key: key,
            id: id,
            idType: idType,
            isRoot: isRoot,
            department: department,
            projectName: projectName,
          ),
          rawPathParams: {'id': id},
          rawQueryParams: {
            'idType': idType,
            'isRoot': isRoot,
            'department': department,
            'projectName': projectName,
          },
          initialChildren: children,
        );

  static const String name = 'FilesRoute';

  static _i24.PageInfo page = _i24.PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final queryParams = data.queryParams;
      final args = data.argsAs<FilesRouteArgs>(
          orElse: () => FilesRouteArgs(
                id: pathParams.optString('id'),
                idType: queryParams.optString('idType'),
                isRoot: queryParams.optBool('isRoot'),
                department: queryParams.optString('department'),
                projectName: queryParams.optString('projectName'),
              ));
      return _i6.FilesScreen(
        key: args.key,
        id: args.id,
        idType: args.idType,
        isRoot: args.isRoot,
        department: args.department,
        projectName: args.projectName,
      );
    },
  );
}

class FilesRouteArgs {
  const FilesRouteArgs({
    this.key,
    this.id,
    this.idType,
    this.isRoot,
    this.department,
    this.projectName,
  });

  final _i25.Key? key;

  final String? id;

  final String? idType;

  final bool? isRoot;

  final String? department;

  final String? projectName;

  @override
  String toString() {
    return 'FilesRouteArgs{key: $key, id: $id, idType: $idType, isRoot: $isRoot, department: $department, projectName: $projectName}';
  }
}

/// generated route for
/// [_i7.InitialScreen]
class InitialRoute extends _i24.PageRouteInfo<void> {
  const InitialRoute({List<_i24.PageRouteInfo>? children})
      : super(
          InitialRoute.name,
          initialChildren: children,
        );

  static const String name = 'InitialRoute';

  static _i24.PageInfo page = _i24.PageInfo(
    name,
    builder: (data) {
      return const _i7.InitialScreen();
    },
  );
}

/// generated route for
/// [_i8.InnerStorageScreen]
class InnerStorageRoute extends _i24.PageRouteInfo<InnerStorageRouteArgs> {
  InnerStorageRoute({
    _i25.Key? key,
    required String id,
    String? name,
    bool isProject = true,
    List<_i24.PageRouteInfo>? children,
  }) : super(
          InnerStorageRoute.name,
          args: InnerStorageRouteArgs(
            key: key,
            id: id,
            name: name,
            isProject: isProject,
          ),
          initialChildren: children,
        );

  static const String name = 'InnerStorageRoute';

  static _i24.PageInfo page = _i24.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<InnerStorageRouteArgs>();
      return _i8.InnerStorageScreen(
        key: args.key,
        id: args.id,
        name: args.name,
        isProject: args.isProject,
      );
    },
  );
}

class InnerStorageRouteArgs {
  const InnerStorageRouteArgs({
    this.key,
    required this.id,
    this.name,
    this.isProject = true,
  });

  final _i25.Key? key;

  final String id;

  final String? name;

  final bool isProject;

  @override
  String toString() {
    return 'InnerStorageRouteArgs{key: $key, id: $id, name: $name, isProject: $isProject}';
  }
}

/// generated route for
/// [_i9.NomenclaturesPage]
class NomenclaturesRoute extends _i24.PageRouteInfo<void> {
  const NomenclaturesRoute({List<_i24.PageRouteInfo>? children})
      : super(
          NomenclaturesRoute.name,
          initialChildren: children,
        );

  static const String name = 'NomenclaturesRoute';

  static _i24.PageInfo page = _i24.PageInfo(
    name,
    builder: (data) {
      return const _i9.NomenclaturesPage();
    },
  );
}

/// generated route for
/// [_i10.NotificationScreen]
class NotificationRoute extends _i24.PageRouteInfo<void> {
  const NotificationRoute({List<_i24.PageRouteInfo>? children})
      : super(
          NotificationRoute.name,
          initialChildren: children,
        );

  static const String name = 'NotificationRoute';

  static _i24.PageInfo page = _i24.PageInfo(
    name,
    builder: (data) {
      return const _i10.NotificationScreen();
    },
  );
}

/// generated route for
/// [_i11.OtkDefectActsScreen]
class OtkDefectActsRoute extends _i24.PageRouteInfo<void> {
  const OtkDefectActsRoute({List<_i24.PageRouteInfo>? children})
      : super(
          OtkDefectActsRoute.name,
          initialChildren: children,
        );

  static const String name = 'OtkDefectActsRoute';

  static _i24.PageInfo page = _i24.PageInfo(
    name,
    builder: (data) {
      return const _i11.OtkDefectActsScreen();
    },
  );
}

/// generated route for
/// [_i12.OtkDeliveriesScreen]
class OtkDeliveriesRoute extends _i24.PageRouteInfo<void> {
  const OtkDeliveriesRoute({List<_i24.PageRouteInfo>? children})
      : super(
          OtkDeliveriesRoute.name,
          initialChildren: children,
        );

  static const String name = 'OtkDeliveriesRoute';

  static _i24.PageInfo page = _i24.PageInfo(
    name,
    builder: (data) {
      return const _i12.OtkDeliveriesScreen();
    },
  );
}

/// generated route for
/// [_i13.PdfViewerScreen]
class PdfViewerRoute extends _i24.PageRouteInfo<PdfViewerRouteArgs> {
  PdfViewerRoute({
    _i25.Key? key,
    required String pdfPath,
    List<_i24.PageRouteInfo>? children,
  }) : super(
          PdfViewerRoute.name,
          args: PdfViewerRouteArgs(
            key: key,
            pdfPath: pdfPath,
          ),
          rawPathParams: {'pdfPath': pdfPath},
          initialChildren: children,
        );

  static const String name = 'PdfViewerRoute';

  static _i24.PageInfo page = _i24.PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<PdfViewerRouteArgs>(
          orElse: () =>
              PdfViewerRouteArgs(pdfPath: pathParams.getString('pdfPath')));
      return _i13.PdfViewerScreen(
        key: args.key,
        pdfPath: args.pdfPath,
      );
    },
  );
}

class PdfViewerRouteArgs {
  const PdfViewerRouteArgs({
    this.key,
    required this.pdfPath,
  });

  final _i25.Key? key;

  final String pdfPath;

  @override
  String toString() {
    return 'PdfViewerRouteArgs{key: $key, pdfPath: $pdfPath}';
  }
}

/// generated route for
/// [_i14.ProductScreen]
class ProductRoute extends _i24.PageRouteInfo<ProductRouteArgs> {
  ProductRoute({
    _i25.Key? key,
    String? id,
    String? productId,
    List<_i24.PageRouteInfo>? children,
  }) : super(
          ProductRoute.name,
          args: ProductRouteArgs(
            key: key,
            id: id,
            productId: productId,
          ),
          rawPathParams: {
            'id': id,
            'productId': productId,
          },
          initialChildren: children,
        );

  static const String name = 'ProductRoute';

  static _i24.PageInfo page = _i24.PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<ProductRouteArgs>(
          orElse: () => ProductRouteArgs(
                id: pathParams.optString('id'),
                productId: pathParams.optString('productId'),
              ));
      return _i14.ProductScreen(
        key: args.key,
        id: args.id,
        productId: args.productId,
      );
    },
  );
}

class ProductRouteArgs {
  const ProductRouteArgs({
    this.key,
    this.id,
    this.productId,
  });

  final _i25.Key? key;

  final String? id;

  final String? productId;

  @override
  String toString() {
    return 'ProductRouteArgs{key: $key, id: $id, productId: $productId}';
  }
}

/// generated route for
/// [_i15.ProductionScreen]
class ProductionRoute extends _i24.PageRouteInfo<void> {
  const ProductionRoute({List<_i24.PageRouteInfo>? children})
      : super(
          ProductionRoute.name,
          initialChildren: children,
        );

  static const String name = 'ProductionRoute';

  static _i24.PageInfo page = _i24.PageInfo(
    name,
    builder: (data) {
      return const _i15.ProductionScreen();
    },
  );
}

/// generated route for
/// [_i16.ProjectScreen]
class ProjectRoute extends _i24.PageRouteInfo<ProjectRouteArgs> {
  ProjectRoute({
    _i25.Key? key,
    String? id,
    List<_i24.PageRouteInfo>? children,
  }) : super(
          ProjectRoute.name,
          args: ProjectRouteArgs(
            key: key,
            id: id,
          ),
          rawPathParams: {'id': id},
          initialChildren: children,
        );

  static const String name = 'ProjectRoute';

  static _i24.PageInfo page = _i24.PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<ProjectRouteArgs>(
          orElse: () => ProjectRouteArgs(id: pathParams.optString('id')));
      return _i16.ProjectScreen(
        key: args.key,
        id: args.id,
      );
    },
  );
}

class ProjectRouteArgs {
  const ProjectRouteArgs({
    this.key,
    this.id,
  });

  final _i25.Key? key;

  final String? id;

  @override
  String toString() {
    return 'ProjectRouteArgs{key: $key, id: $id}';
  }
}

/// generated route for
/// [_i17.PurchaseListScreen]
class PurchaseListRoute extends _i24.PageRouteInfo<PurchaseListRouteArgs> {
  PurchaseListRoute({
    _i26.Key? key,
    required String id,
    required String projectName,
    List<_i24.PageRouteInfo>? children,
  }) : super(
          PurchaseListRoute.name,
          args: PurchaseListRouteArgs(
            key: key,
            id: id,
            projectName: projectName,
          ),
          rawPathParams: {'id': id},
          initialChildren: children,
        );

  static const String name = 'PurchaseListRoute';

  static _i24.PageInfo page = _i24.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<PurchaseListRouteArgs>();
      return _i17.PurchaseListScreen(
        key: args.key,
        id: args.id,
        projectName: args.projectName,
      );
    },
  );
}

class PurchaseListRouteArgs {
  const PurchaseListRouteArgs({
    this.key,
    required this.id,
    required this.projectName,
  });

  final _i26.Key? key;

  final String id;

  final String projectName;

  @override
  String toString() {
    return 'PurchaseListRouteArgs{key: $key, id: $id, projectName: $projectName}';
  }
}

/// generated route for
/// [_i18.SettingsScreen]
class SettingsRoute extends _i24.PageRouteInfo<void> {
  const SettingsRoute({List<_i24.PageRouteInfo>? children})
      : super(
          SettingsRoute.name,
          initialChildren: children,
        );

  static const String name = 'SettingsRoute';

  static _i24.PageInfo page = _i24.PageInfo(
    name,
    builder: (data) {
      return const _i18.SettingsScreen();
    },
  );
}

/// generated route for
/// [_i19.SplashScreen]
class SplashRoute extends _i24.PageRouteInfo<void> {
  const SplashRoute({List<_i24.PageRouteInfo>? children})
      : super(
          SplashRoute.name,
          initialChildren: children,
        );

  static const String name = 'SplashRoute';

  static _i24.PageInfo page = _i24.PageInfo(
    name,
    builder: (data) {
      return const _i19.SplashScreen();
    },
  );
}

/// generated route for
/// [_i20.StorageScreen]
class StorageRoute extends _i24.PageRouteInfo<void> {
  const StorageRoute({List<_i24.PageRouteInfo>? children})
      : super(
          StorageRoute.name,
          initialChildren: children,
        );

  static const String name = 'StorageRoute';

  static _i24.PageInfo page = _i24.PageInfo(
    name,
    builder: (data) {
      return const _i20.StorageScreen();
    },
  );
}

/// generated route for
/// [_i21.StructureScreen]
class StructureRoute extends _i24.PageRouteInfo<StructureRouteArgs> {
  StructureRoute({
    _i25.Key? key,
    String? id,
    bool? isProduct,
    String? projectId,
    List<_i24.PageRouteInfo>? children,
  }) : super(
          StructureRoute.name,
          args: StructureRouteArgs(
            key: key,
            id: id,
            isProduct: isProduct,
            projectId: projectId,
          ),
          rawPathParams: {'id': id},
          initialChildren: children,
        );

  static const String name = 'StructureRoute';

  static _i24.PageInfo page = _i24.PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<StructureRouteArgs>(
          orElse: () => StructureRouteArgs(id: pathParams.optString('id')));
      return _i21.StructureScreen(
        key: args.key,
        id: args.id,
        isProduct: args.isProduct,
        projectId: args.projectId,
      );
    },
  );
}

class StructureRouteArgs {
  const StructureRouteArgs({
    this.key,
    this.id,
    this.isProduct,
    this.projectId,
  });

  final _i25.Key? key;

  final String? id;

  final bool? isProduct;

  final String? projectId;

  @override
  String toString() {
    return 'StructureRouteArgs{key: $key, id: $id, isProduct: $isProduct, projectId: $projectId}';
  }
}

/// generated route for
/// [_i22.WarehouseContentsScreen]
class WarehouseContentsRoute
    extends _i24.PageRouteInfo<WarehouseContentsRouteArgs> {
  WarehouseContentsRoute({
    _i25.Key? key,
    required String warehouseId,
    List<_i24.PageRouteInfo>? children,
  }) : super(
          WarehouseContentsRoute.name,
          args: WarehouseContentsRouteArgs(
            key: key,
            warehouseId: warehouseId,
          ),
          rawPathParams: {'warehouseId': warehouseId},
          initialChildren: children,
        );

  static const String name = 'WarehouseContentsRoute';

  static _i24.PageInfo page = _i24.PageInfo(
    name,
    builder: (data) {
      final pathParams = data.inheritedPathParams;
      final args = data.argsAs<WarehouseContentsRouteArgs>(
          orElse: () => WarehouseContentsRouteArgs(
              warehouseId: pathParams.getString('warehouseId')));
      return _i22.WarehouseContentsScreen(
        key: args.key,
        warehouseId: args.warehouseId,
      );
    },
  );
}

class WarehouseContentsRouteArgs {
  const WarehouseContentsRouteArgs({
    this.key,
    required this.warehouseId,
  });

  final _i25.Key? key;

  final String warehouseId;

  @override
  String toString() {
    return 'WarehouseContentsRouteArgs{key: $key, warehouseId: $warehouseId}';
  }
}

/// generated route for
/// [_i23.WarehousesScreen]
class WarehousesRoute extends _i24.PageRouteInfo<void> {
  const WarehousesRoute({List<_i24.PageRouteInfo>? children})
      : super(
          WarehousesRoute.name,
          initialChildren: children,
        );

  static const String name = 'WarehousesRoute';

  static _i24.PageInfo page = _i24.PageInfo(
    name,
    builder: (data) {
      return const _i23.WarehousesScreen();
    },
  );
}
