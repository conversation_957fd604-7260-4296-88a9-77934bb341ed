import 'package:flutter/material.dart';
import 'package:sphere/features/_initial/production/data/models/index.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';
import 'package:sphere/shared/widgets/interactive/ghost_button.dart';

class UpdateStatusDialog extends StatefulWidget {
  final ProductionTaskModel task;
  final Function(ProductionTaskUpdateStatusRequest request) onStatusUpdated;

  const UpdateStatusDialog({
    super.key,
    required this.task,
    required this.onStatusUpdated,
  });

  @override
  State<UpdateStatusDialog> createState() => _UpdateStatusDialogState();
}

class _UpdateStatusDialogState extends State<UpdateStatusDialog> {
  final _formKey = GlobalKey<FormState>();
  final _commentController = TextEditingController();
  ProductionTaskStatus? _selectedStatus;

  @override
  void initState() {
    super.initState();
    _selectedStatus = widget.task.status;
  }

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  void _updateStatus() {
    if (_formKey.currentState?.validate() ?? false) {
      if (_selectedStatus == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Выберите статус')),
        );
        return;
      }

      final request = ProductionTaskUpdateStatusRequest(
        taskId: widget.task.id,
        status: _selectedStatus!,
        comment: _commentController.text.trim().isEmpty
            ? null
            : _commentController.text.trim(),
      );

      widget.onStatusUpdated(request);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 500,
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Обновить статус задания',
                style: Fonts.headlineSmall,
              ),
              const SizedBox(height: 24),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Theme.of(context).dividerColor,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Информация о задании',
                      style: Fonts.labelMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'ID: ${widget.task.id?.substring(0, 8) ?? 'N/A'}',
                      style: Fonts.bodySmall.copyWith(
                        color: Theme.of(context).textTheme.bodySmall?.color,
                      ),
                    ),
                    Text(
                      'Продукт: ${widget.task.productId ?? 'N/A'}',
                      style: Fonts.bodySmall.copyWith(
                        color: Theme.of(context).textTheme.bodySmall?.color,
                      ),
                    ),
                    Text(
                      'Текущий статус: ${widget.task.status?.getName() ?? 'Неизвестно'}',
                      style: Fonts.bodySmall.copyWith(
                        color: Theme.of(context).textTheme.bodySmall?.color,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<ProductionTaskStatus>(
                value: _selectedStatus,
                decoration: const InputDecoration(
                  labelText: 'Новый статус *',
                  border: OutlineInputBorder(),
                ),
                items: ProductionTaskStatus.values
                    .map(
                      (status) => DropdownMenuItem<ProductionTaskStatus>(
                        value: status,
                        child: Row(
                          children: [
                            Container(
                              width: 12,
                              height: 12,
                              decoration: BoxDecoration(
                                color: _getStatusColor(status),
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(status.getName()),
                          ],
                        ),
                      ),
                    )
                    .toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedStatus = value;
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return 'Выберите статус';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _commentController,
                decoration: const InputDecoration(
                  labelText: 'Комментарий',
                  hintText: 'Добавьте комментарий (необязательно)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  GhostButton(
                    onTap: () => Navigator.of(context).pop(),
                    child: const Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      child: Text('Отмена'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  CustomElevatedButton(
                    onPressed: _updateStatus,
                    text: 'Обновить статус',
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(ProductionTaskStatus status) {
    switch (status) {
      case ProductionTaskStatus.materialsRequested:
        return AppColors.lightWarning;
      case ProductionTaskStatus.materialsIssued:
        return AppColors.lightPurple;
      case ProductionTaskStatus.assemblyInProgress:
        return AppColors.lightPrimary;
      case ProductionTaskStatus.readyForInspection:
        return AppColors.lightPurple;
      case ProductionTaskStatus.qcInspection:
        return AppColors.lightWarning;
      case ProductionTaskStatus.qcApproved:
        return AppColors.lightSuccess;
    }
  }
}
