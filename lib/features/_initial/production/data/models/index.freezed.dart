// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'index.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ProductionItemsRequest _$ProductionItemsRequestFromJson(
    Map<String, dynamic> json) {
  return _ProductionItemsRequest.fromJson(json);
}

/// @nodoc
mixin _$ProductionItemsRequest {
  String? get projectId => throw _privateConstructorUsedError;

  /// Serializes this ProductionItemsRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductionItemsRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductionItemsRequestCopyWith<ProductionItemsRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductionItemsRequestCopyWith<$Res> {
  factory $ProductionItemsRequestCopyWith(ProductionItemsRequest value,
          $Res Function(ProductionItemsRequest) then) =
      _$ProductionItemsRequestCopyWithImpl<$Res, ProductionItemsRequest>;
  @useResult
  $Res call({String? projectId});
}

/// @nodoc
class _$ProductionItemsRequestCopyWithImpl<$Res,
        $Val extends ProductionItemsRequest>
    implements $ProductionItemsRequestCopyWith<$Res> {
  _$ProductionItemsRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductionItemsRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? projectId = freezed,
  }) {
    return _then(_value.copyWith(
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductionItemsRequestImplCopyWith<$Res>
    implements $ProductionItemsRequestCopyWith<$Res> {
  factory _$$ProductionItemsRequestImplCopyWith(
          _$ProductionItemsRequestImpl value,
          $Res Function(_$ProductionItemsRequestImpl) then) =
      __$$ProductionItemsRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? projectId});
}

/// @nodoc
class __$$ProductionItemsRequestImplCopyWithImpl<$Res>
    extends _$ProductionItemsRequestCopyWithImpl<$Res,
        _$ProductionItemsRequestImpl>
    implements _$$ProductionItemsRequestImplCopyWith<$Res> {
  __$$ProductionItemsRequestImplCopyWithImpl(
      _$ProductionItemsRequestImpl _value,
      $Res Function(_$ProductionItemsRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductionItemsRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? projectId = freezed,
  }) {
    return _then(_$ProductionItemsRequestImpl(
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProductionItemsRequestImpl implements _ProductionItemsRequest {
  const _$ProductionItemsRequestImpl({this.projectId});

  factory _$ProductionItemsRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductionItemsRequestImplFromJson(json);

  @override
  final String? projectId;

  @override
  String toString() {
    return 'ProductionItemsRequest(projectId: $projectId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductionItemsRequestImpl &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, projectId);

  /// Create a copy of ProductionItemsRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductionItemsRequestImplCopyWith<_$ProductionItemsRequestImpl>
      get copyWith => __$$ProductionItemsRequestImplCopyWithImpl<
          _$ProductionItemsRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductionItemsRequestImplToJson(
      this,
    );
  }
}

abstract class _ProductionItemsRequest implements ProductionItemsRequest {
  const factory _ProductionItemsRequest({final String? projectId}) =
      _$ProductionItemsRequestImpl;

  factory _ProductionItemsRequest.fromJson(Map<String, dynamic> json) =
      _$ProductionItemsRequestImpl.fromJson;

  @override
  String? get projectId;

  /// Create a copy of ProductionItemsRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductionItemsRequestImplCopyWith<_$ProductionItemsRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProductionTaskCancelRequest _$ProductionTaskCancelRequestFromJson(
    Map<String, dynamic> json) {
  return _ProductionTaskCancelRequest.fromJson(json);
}

/// @nodoc
mixin _$ProductionTaskCancelRequest {
  String? get taskId => throw _privateConstructorUsedError;
  String? get reason => throw _privateConstructorUsedError;

  /// Serializes this ProductionTaskCancelRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductionTaskCancelRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductionTaskCancelRequestCopyWith<ProductionTaskCancelRequest>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductionTaskCancelRequestCopyWith<$Res> {
  factory $ProductionTaskCancelRequestCopyWith(
          ProductionTaskCancelRequest value,
          $Res Function(ProductionTaskCancelRequest) then) =
      _$ProductionTaskCancelRequestCopyWithImpl<$Res,
          ProductionTaskCancelRequest>;
  @useResult
  $Res call({String? taskId, String? reason});
}

/// @nodoc
class _$ProductionTaskCancelRequestCopyWithImpl<$Res,
        $Val extends ProductionTaskCancelRequest>
    implements $ProductionTaskCancelRequestCopyWith<$Res> {
  _$ProductionTaskCancelRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductionTaskCancelRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskId = freezed,
    Object? reason = freezed,
  }) {
    return _then(_value.copyWith(
      taskId: freezed == taskId
          ? _value.taskId
          : taskId // ignore: cast_nullable_to_non_nullable
              as String?,
      reason: freezed == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductionTaskCancelRequestImplCopyWith<$Res>
    implements $ProductionTaskCancelRequestCopyWith<$Res> {
  factory _$$ProductionTaskCancelRequestImplCopyWith(
          _$ProductionTaskCancelRequestImpl value,
          $Res Function(_$ProductionTaskCancelRequestImpl) then) =
      __$$ProductionTaskCancelRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? taskId, String? reason});
}

/// @nodoc
class __$$ProductionTaskCancelRequestImplCopyWithImpl<$Res>
    extends _$ProductionTaskCancelRequestCopyWithImpl<$Res,
        _$ProductionTaskCancelRequestImpl>
    implements _$$ProductionTaskCancelRequestImplCopyWith<$Res> {
  __$$ProductionTaskCancelRequestImplCopyWithImpl(
      _$ProductionTaskCancelRequestImpl _value,
      $Res Function(_$ProductionTaskCancelRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductionTaskCancelRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskId = freezed,
    Object? reason = freezed,
  }) {
    return _then(_$ProductionTaskCancelRequestImpl(
      taskId: freezed == taskId
          ? _value.taskId
          : taskId // ignore: cast_nullable_to_non_nullable
              as String?,
      reason: freezed == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProductionTaskCancelRequestImpl
    implements _ProductionTaskCancelRequest {
  const _$ProductionTaskCancelRequestImpl({this.taskId, this.reason});

  factory _$ProductionTaskCancelRequestImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$ProductionTaskCancelRequestImplFromJson(json);

  @override
  final String? taskId;
  @override
  final String? reason;

  @override
  String toString() {
    return 'ProductionTaskCancelRequest(taskId: $taskId, reason: $reason)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductionTaskCancelRequestImpl &&
            (identical(other.taskId, taskId) || other.taskId == taskId) &&
            (identical(other.reason, reason) || other.reason == reason));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, taskId, reason);

  /// Create a copy of ProductionTaskCancelRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductionTaskCancelRequestImplCopyWith<_$ProductionTaskCancelRequestImpl>
      get copyWith => __$$ProductionTaskCancelRequestImplCopyWithImpl<
          _$ProductionTaskCancelRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductionTaskCancelRequestImplToJson(
      this,
    );
  }
}

abstract class _ProductionTaskCancelRequest
    implements ProductionTaskCancelRequest {
  const factory _ProductionTaskCancelRequest(
      {final String? taskId,
      final String? reason}) = _$ProductionTaskCancelRequestImpl;

  factory _ProductionTaskCancelRequest.fromJson(Map<String, dynamic> json) =
      _$ProductionTaskCancelRequestImpl.fromJson;

  @override
  String? get taskId;
  @override
  String? get reason;

  /// Create a copy of ProductionTaskCancelRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductionTaskCancelRequestImplCopyWith<_$ProductionTaskCancelRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProductionTaskCreateRequest _$ProductionTaskCreateRequestFromJson(
    Map<String, dynamic> json) {
  return _ProductionTaskCreateRequest.fromJson(json);
}

/// @nodoc
mixin _$ProductionTaskCreateRequest {
  String? get projectId => throw _privateConstructorUsedError;
  String? get productId => throw _privateConstructorUsedError;
  double? get quantityRequired => throw _privateConstructorUsedError;

  /// Serializes this ProductionTaskCreateRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductionTaskCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductionTaskCreateRequestCopyWith<ProductionTaskCreateRequest>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductionTaskCreateRequestCopyWith<$Res> {
  factory $ProductionTaskCreateRequestCopyWith(
          ProductionTaskCreateRequest value,
          $Res Function(ProductionTaskCreateRequest) then) =
      _$ProductionTaskCreateRequestCopyWithImpl<$Res,
          ProductionTaskCreateRequest>;
  @useResult
  $Res call({String? projectId, String? productId, double? quantityRequired});
}

/// @nodoc
class _$ProductionTaskCreateRequestCopyWithImpl<$Res,
        $Val extends ProductionTaskCreateRequest>
    implements $ProductionTaskCreateRequestCopyWith<$Res> {
  _$ProductionTaskCreateRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductionTaskCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? projectId = freezed,
    Object? productId = freezed,
    Object? quantityRequired = freezed,
  }) {
    return _then(_value.copyWith(
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      quantityRequired: freezed == quantityRequired
          ? _value.quantityRequired
          : quantityRequired // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductionTaskCreateRequestImplCopyWith<$Res>
    implements $ProductionTaskCreateRequestCopyWith<$Res> {
  factory _$$ProductionTaskCreateRequestImplCopyWith(
          _$ProductionTaskCreateRequestImpl value,
          $Res Function(_$ProductionTaskCreateRequestImpl) then) =
      __$$ProductionTaskCreateRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? projectId, String? productId, double? quantityRequired});
}

/// @nodoc
class __$$ProductionTaskCreateRequestImplCopyWithImpl<$Res>
    extends _$ProductionTaskCreateRequestCopyWithImpl<$Res,
        _$ProductionTaskCreateRequestImpl>
    implements _$$ProductionTaskCreateRequestImplCopyWith<$Res> {
  __$$ProductionTaskCreateRequestImplCopyWithImpl(
      _$ProductionTaskCreateRequestImpl _value,
      $Res Function(_$ProductionTaskCreateRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductionTaskCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? projectId = freezed,
    Object? productId = freezed,
    Object? quantityRequired = freezed,
  }) {
    return _then(_$ProductionTaskCreateRequestImpl(
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      quantityRequired: freezed == quantityRequired
          ? _value.quantityRequired
          : quantityRequired // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProductionTaskCreateRequestImpl
    implements _ProductionTaskCreateRequest {
  const _$ProductionTaskCreateRequestImpl(
      {this.projectId, this.productId, this.quantityRequired});

  factory _$ProductionTaskCreateRequestImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$ProductionTaskCreateRequestImplFromJson(json);

  @override
  final String? projectId;
  @override
  final String? productId;
  @override
  final double? quantityRequired;

  @override
  String toString() {
    return 'ProductionTaskCreateRequest(projectId: $projectId, productId: $productId, quantityRequired: $quantityRequired)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductionTaskCreateRequestImpl &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.quantityRequired, quantityRequired) ||
                other.quantityRequired == quantityRequired));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, projectId, productId, quantityRequired);

  /// Create a copy of ProductionTaskCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductionTaskCreateRequestImplCopyWith<_$ProductionTaskCreateRequestImpl>
      get copyWith => __$$ProductionTaskCreateRequestImplCopyWithImpl<
          _$ProductionTaskCreateRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductionTaskCreateRequestImplToJson(
      this,
    );
  }
}

abstract class _ProductionTaskCreateRequest
    implements ProductionTaskCreateRequest {
  const factory _ProductionTaskCreateRequest(
      {final String? projectId,
      final String? productId,
      final double? quantityRequired}) = _$ProductionTaskCreateRequestImpl;

  factory _ProductionTaskCreateRequest.fromJson(Map<String, dynamic> json) =
      _$ProductionTaskCreateRequestImpl.fromJson;

  @override
  String? get projectId;
  @override
  String? get productId;
  @override
  double? get quantityRequired;

  /// Create a copy of ProductionTaskCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductionTaskCreateRequestImplCopyWith<_$ProductionTaskCreateRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProductionTaskDetailsRequest _$ProductionTaskDetailsRequestFromJson(
    Map<String, dynamic> json) {
  return _ProductionTaskDetailsRequest.fromJson(json);
}

/// @nodoc
mixin _$ProductionTaskDetailsRequest {
  String? get taskId => throw _privateConstructorUsedError;

  /// Serializes this ProductionTaskDetailsRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductionTaskDetailsRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductionTaskDetailsRequestCopyWith<ProductionTaskDetailsRequest>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductionTaskDetailsRequestCopyWith<$Res> {
  factory $ProductionTaskDetailsRequestCopyWith(
          ProductionTaskDetailsRequest value,
          $Res Function(ProductionTaskDetailsRequest) then) =
      _$ProductionTaskDetailsRequestCopyWithImpl<$Res,
          ProductionTaskDetailsRequest>;
  @useResult
  $Res call({String? taskId});
}

/// @nodoc
class _$ProductionTaskDetailsRequestCopyWithImpl<$Res,
        $Val extends ProductionTaskDetailsRequest>
    implements $ProductionTaskDetailsRequestCopyWith<$Res> {
  _$ProductionTaskDetailsRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductionTaskDetailsRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskId = freezed,
  }) {
    return _then(_value.copyWith(
      taskId: freezed == taskId
          ? _value.taskId
          : taskId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductionTaskDetailsRequestImplCopyWith<$Res>
    implements $ProductionTaskDetailsRequestCopyWith<$Res> {
  factory _$$ProductionTaskDetailsRequestImplCopyWith(
          _$ProductionTaskDetailsRequestImpl value,
          $Res Function(_$ProductionTaskDetailsRequestImpl) then) =
      __$$ProductionTaskDetailsRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? taskId});
}

/// @nodoc
class __$$ProductionTaskDetailsRequestImplCopyWithImpl<$Res>
    extends _$ProductionTaskDetailsRequestCopyWithImpl<$Res,
        _$ProductionTaskDetailsRequestImpl>
    implements _$$ProductionTaskDetailsRequestImplCopyWith<$Res> {
  __$$ProductionTaskDetailsRequestImplCopyWithImpl(
      _$ProductionTaskDetailsRequestImpl _value,
      $Res Function(_$ProductionTaskDetailsRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductionTaskDetailsRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskId = freezed,
  }) {
    return _then(_$ProductionTaskDetailsRequestImpl(
      taskId: freezed == taskId
          ? _value.taskId
          : taskId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProductionTaskDetailsRequestImpl
    implements _ProductionTaskDetailsRequest {
  const _$ProductionTaskDetailsRequestImpl({this.taskId});

  factory _$ProductionTaskDetailsRequestImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$ProductionTaskDetailsRequestImplFromJson(json);

  @override
  final String? taskId;

  @override
  String toString() {
    return 'ProductionTaskDetailsRequest(taskId: $taskId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductionTaskDetailsRequestImpl &&
            (identical(other.taskId, taskId) || other.taskId == taskId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, taskId);

  /// Create a copy of ProductionTaskDetailsRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductionTaskDetailsRequestImplCopyWith<
          _$ProductionTaskDetailsRequestImpl>
      get copyWith => __$$ProductionTaskDetailsRequestImplCopyWithImpl<
          _$ProductionTaskDetailsRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductionTaskDetailsRequestImplToJson(
      this,
    );
  }
}

abstract class _ProductionTaskDetailsRequest
    implements ProductionTaskDetailsRequest {
  const factory _ProductionTaskDetailsRequest({final String? taskId}) =
      _$ProductionTaskDetailsRequestImpl;

  factory _ProductionTaskDetailsRequest.fromJson(Map<String, dynamic> json) =
      _$ProductionTaskDetailsRequestImpl.fromJson;

  @override
  String? get taskId;

  /// Create a copy of ProductionTaskDetailsRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductionTaskDetailsRequestImplCopyWith<
          _$ProductionTaskDetailsRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProductionTaskUpdateStatusRequest _$ProductionTaskUpdateStatusRequestFromJson(
    Map<String, dynamic> json) {
  return _ProductionTaskUpdateStatusRequest.fromJson(json);
}

/// @nodoc
mixin _$ProductionTaskUpdateStatusRequest {
  String? get taskId => throw _privateConstructorUsedError;
  ProductionTaskStatus? get status => throw _privateConstructorUsedError;
  String? get comment => throw _privateConstructorUsedError;

  /// Serializes this ProductionTaskUpdateStatusRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductionTaskUpdateStatusRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductionTaskUpdateStatusRequestCopyWith<ProductionTaskUpdateStatusRequest>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductionTaskUpdateStatusRequestCopyWith<$Res> {
  factory $ProductionTaskUpdateStatusRequestCopyWith(
          ProductionTaskUpdateStatusRequest value,
          $Res Function(ProductionTaskUpdateStatusRequest) then) =
      _$ProductionTaskUpdateStatusRequestCopyWithImpl<$Res,
          ProductionTaskUpdateStatusRequest>;
  @useResult
  $Res call({String? taskId, ProductionTaskStatus? status, String? comment});
}

/// @nodoc
class _$ProductionTaskUpdateStatusRequestCopyWithImpl<$Res,
        $Val extends ProductionTaskUpdateStatusRequest>
    implements $ProductionTaskUpdateStatusRequestCopyWith<$Res> {
  _$ProductionTaskUpdateStatusRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductionTaskUpdateStatusRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskId = freezed,
    Object? status = freezed,
    Object? comment = freezed,
  }) {
    return _then(_value.copyWith(
      taskId: freezed == taskId
          ? _value.taskId
          : taskId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ProductionTaskStatus?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductionTaskUpdateStatusRequestImplCopyWith<$Res>
    implements $ProductionTaskUpdateStatusRequestCopyWith<$Res> {
  factory _$$ProductionTaskUpdateStatusRequestImplCopyWith(
          _$ProductionTaskUpdateStatusRequestImpl value,
          $Res Function(_$ProductionTaskUpdateStatusRequestImpl) then) =
      __$$ProductionTaskUpdateStatusRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? taskId, ProductionTaskStatus? status, String? comment});
}

/// @nodoc
class __$$ProductionTaskUpdateStatusRequestImplCopyWithImpl<$Res>
    extends _$ProductionTaskUpdateStatusRequestCopyWithImpl<$Res,
        _$ProductionTaskUpdateStatusRequestImpl>
    implements _$$ProductionTaskUpdateStatusRequestImplCopyWith<$Res> {
  __$$ProductionTaskUpdateStatusRequestImplCopyWithImpl(
      _$ProductionTaskUpdateStatusRequestImpl _value,
      $Res Function(_$ProductionTaskUpdateStatusRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductionTaskUpdateStatusRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskId = freezed,
    Object? status = freezed,
    Object? comment = freezed,
  }) {
    return _then(_$ProductionTaskUpdateStatusRequestImpl(
      taskId: freezed == taskId
          ? _value.taskId
          : taskId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ProductionTaskStatus?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProductionTaskUpdateStatusRequestImpl
    implements _ProductionTaskUpdateStatusRequest {
  const _$ProductionTaskUpdateStatusRequestImpl(
      {this.taskId, this.status, this.comment});

  factory _$ProductionTaskUpdateStatusRequestImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$ProductionTaskUpdateStatusRequestImplFromJson(json);

  @override
  final String? taskId;
  @override
  final ProductionTaskStatus? status;
  @override
  final String? comment;

  @override
  String toString() {
    return 'ProductionTaskUpdateStatusRequest(taskId: $taskId, status: $status, comment: $comment)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductionTaskUpdateStatusRequestImpl &&
            (identical(other.taskId, taskId) || other.taskId == taskId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.comment, comment) || other.comment == comment));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, taskId, status, comment);

  /// Create a copy of ProductionTaskUpdateStatusRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductionTaskUpdateStatusRequestImplCopyWith<
          _$ProductionTaskUpdateStatusRequestImpl>
      get copyWith => __$$ProductionTaskUpdateStatusRequestImplCopyWithImpl<
          _$ProductionTaskUpdateStatusRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductionTaskUpdateStatusRequestImplToJson(
      this,
    );
  }
}

abstract class _ProductionTaskUpdateStatusRequest
    implements ProductionTaskUpdateStatusRequest {
  const factory _ProductionTaskUpdateStatusRequest(
      {final String? taskId,
      final ProductionTaskStatus? status,
      final String? comment}) = _$ProductionTaskUpdateStatusRequestImpl;

  factory _ProductionTaskUpdateStatusRequest.fromJson(
          Map<String, dynamic> json) =
      _$ProductionTaskUpdateStatusRequestImpl.fromJson;

  @override
  String? get taskId;
  @override
  ProductionTaskStatus? get status;
  @override
  String? get comment;

  /// Create a copy of ProductionTaskUpdateStatusRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductionTaskUpdateStatusRequestImplCopyWith<
          _$ProductionTaskUpdateStatusRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProductionTasksRequest _$ProductionTasksRequestFromJson(
    Map<String, dynamic> json) {
  return _ProductionTasksRequest.fromJson(json);
}

/// @nodoc
mixin _$ProductionTasksRequest {
  String? get projectId => throw _privateConstructorUsedError;
  String? get productId => throw _privateConstructorUsedError;
  ProductionTaskStatus? get status => throw _privateConstructorUsedError;
  DateTime? get dateFrom => throw _privateConstructorUsedError;
  DateTime? get dateTo => throw _privateConstructorUsedError;

  /// Serializes this ProductionTasksRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductionTasksRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductionTasksRequestCopyWith<ProductionTasksRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductionTasksRequestCopyWith<$Res> {
  factory $ProductionTasksRequestCopyWith(ProductionTasksRequest value,
          $Res Function(ProductionTasksRequest) then) =
      _$ProductionTasksRequestCopyWithImpl<$Res, ProductionTasksRequest>;
  @useResult
  $Res call(
      {String? projectId,
      String? productId,
      ProductionTaskStatus? status,
      DateTime? dateFrom,
      DateTime? dateTo});
}

/// @nodoc
class _$ProductionTasksRequestCopyWithImpl<$Res,
        $Val extends ProductionTasksRequest>
    implements $ProductionTasksRequestCopyWith<$Res> {
  _$ProductionTasksRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductionTasksRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? projectId = freezed,
    Object? productId = freezed,
    Object? status = freezed,
    Object? dateFrom = freezed,
    Object? dateTo = freezed,
  }) {
    return _then(_value.copyWith(
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ProductionTaskStatus?,
      dateFrom: freezed == dateFrom
          ? _value.dateFrom
          : dateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      dateTo: freezed == dateTo
          ? _value.dateTo
          : dateTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductionTasksRequestImplCopyWith<$Res>
    implements $ProductionTasksRequestCopyWith<$Res> {
  factory _$$ProductionTasksRequestImplCopyWith(
          _$ProductionTasksRequestImpl value,
          $Res Function(_$ProductionTasksRequestImpl) then) =
      __$$ProductionTasksRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? projectId,
      String? productId,
      ProductionTaskStatus? status,
      DateTime? dateFrom,
      DateTime? dateTo});
}

/// @nodoc
class __$$ProductionTasksRequestImplCopyWithImpl<$Res>
    extends _$ProductionTasksRequestCopyWithImpl<$Res,
        _$ProductionTasksRequestImpl>
    implements _$$ProductionTasksRequestImplCopyWith<$Res> {
  __$$ProductionTasksRequestImplCopyWithImpl(
      _$ProductionTasksRequestImpl _value,
      $Res Function(_$ProductionTasksRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductionTasksRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? projectId = freezed,
    Object? productId = freezed,
    Object? status = freezed,
    Object? dateFrom = freezed,
    Object? dateTo = freezed,
  }) {
    return _then(_$ProductionTasksRequestImpl(
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ProductionTaskStatus?,
      dateFrom: freezed == dateFrom
          ? _value.dateFrom
          : dateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      dateTo: freezed == dateTo
          ? _value.dateTo
          : dateTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProductionTasksRequestImpl implements _ProductionTasksRequest {
  const _$ProductionTasksRequestImpl(
      {this.projectId,
      this.productId,
      this.status,
      this.dateFrom,
      this.dateTo});

  factory _$ProductionTasksRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductionTasksRequestImplFromJson(json);

  @override
  final String? projectId;
  @override
  final String? productId;
  @override
  final ProductionTaskStatus? status;
  @override
  final DateTime? dateFrom;
  @override
  final DateTime? dateTo;

  @override
  String toString() {
    return 'ProductionTasksRequest(projectId: $projectId, productId: $productId, status: $status, dateFrom: $dateFrom, dateTo: $dateTo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductionTasksRequestImpl &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.dateFrom, dateFrom) ||
                other.dateFrom == dateFrom) &&
            (identical(other.dateTo, dateTo) || other.dateTo == dateTo));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, projectId, productId, status, dateFrom, dateTo);

  /// Create a copy of ProductionTasksRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductionTasksRequestImplCopyWith<_$ProductionTasksRequestImpl>
      get copyWith => __$$ProductionTasksRequestImplCopyWithImpl<
          _$ProductionTasksRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductionTasksRequestImplToJson(
      this,
    );
  }
}

abstract class _ProductionTasksRequest implements ProductionTasksRequest {
  const factory _ProductionTasksRequest(
      {final String? projectId,
      final String? productId,
      final ProductionTaskStatus? status,
      final DateTime? dateFrom,
      final DateTime? dateTo}) = _$ProductionTasksRequestImpl;

  factory _ProductionTasksRequest.fromJson(Map<String, dynamic> json) =
      _$ProductionTasksRequestImpl.fromJson;

  @override
  String? get projectId;
  @override
  String? get productId;
  @override
  ProductionTaskStatus? get status;
  @override
  DateTime? get dateFrom;
  @override
  DateTime? get dateTo;

  /// Create a copy of ProductionTasksRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductionTasksRequestImplCopyWith<_$ProductionTasksRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProductionItemsResponse _$ProductionItemsResponseFromJson(
    Map<String, dynamic> json) {
  return _ProductionItemsResponse.fromJson(json);
}

/// @nodoc
mixin _$ProductionItemsResponse {
  List<ProductionItemModel>? get items => throw _privateConstructorUsedError;
  int? get totalItems => throw _privateConstructorUsedError;

  /// Serializes this ProductionItemsResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductionItemsResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductionItemsResponseCopyWith<ProductionItemsResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductionItemsResponseCopyWith<$Res> {
  factory $ProductionItemsResponseCopyWith(ProductionItemsResponse value,
          $Res Function(ProductionItemsResponse) then) =
      _$ProductionItemsResponseCopyWithImpl<$Res, ProductionItemsResponse>;
  @useResult
  $Res call({List<ProductionItemModel>? items, int? totalItems});
}

/// @nodoc
class _$ProductionItemsResponseCopyWithImpl<$Res,
        $Val extends ProductionItemsResponse>
    implements $ProductionItemsResponseCopyWith<$Res> {
  _$ProductionItemsResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductionItemsResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = freezed,
    Object? totalItems = freezed,
  }) {
    return _then(_value.copyWith(
      items: freezed == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ProductionItemModel>?,
      totalItems: freezed == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductionItemsResponseImplCopyWith<$Res>
    implements $ProductionItemsResponseCopyWith<$Res> {
  factory _$$ProductionItemsResponseImplCopyWith(
          _$ProductionItemsResponseImpl value,
          $Res Function(_$ProductionItemsResponseImpl) then) =
      __$$ProductionItemsResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<ProductionItemModel>? items, int? totalItems});
}

/// @nodoc
class __$$ProductionItemsResponseImplCopyWithImpl<$Res>
    extends _$ProductionItemsResponseCopyWithImpl<$Res,
        _$ProductionItemsResponseImpl>
    implements _$$ProductionItemsResponseImplCopyWith<$Res> {
  __$$ProductionItemsResponseImplCopyWithImpl(
      _$ProductionItemsResponseImpl _value,
      $Res Function(_$ProductionItemsResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductionItemsResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = freezed,
    Object? totalItems = freezed,
  }) {
    return _then(_$ProductionItemsResponseImpl(
      items: freezed == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ProductionItemModel>?,
      totalItems: freezed == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProductionItemsResponseImpl implements _ProductionItemsResponse {
  const _$ProductionItemsResponseImpl(
      {final List<ProductionItemModel>? items, this.totalItems})
      : _items = items;

  factory _$ProductionItemsResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductionItemsResponseImplFromJson(json);

  final List<ProductionItemModel>? _items;
  @override
  List<ProductionItemModel>? get items {
    final value = _items;
    if (value == null) return null;
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? totalItems;

  @override
  String toString() {
    return 'ProductionItemsResponse(items: $items, totalItems: $totalItems)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductionItemsResponseImpl &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            (identical(other.totalItems, totalItems) ||
                other.totalItems == totalItems));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_items), totalItems);

  /// Create a copy of ProductionItemsResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductionItemsResponseImplCopyWith<_$ProductionItemsResponseImpl>
      get copyWith => __$$ProductionItemsResponseImplCopyWithImpl<
          _$ProductionItemsResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductionItemsResponseImplToJson(
      this,
    );
  }
}

abstract class _ProductionItemsResponse implements ProductionItemsResponse {
  const factory _ProductionItemsResponse(
      {final List<ProductionItemModel>? items,
      final int? totalItems}) = _$ProductionItemsResponseImpl;

  factory _ProductionItemsResponse.fromJson(Map<String, dynamic> json) =
      _$ProductionItemsResponseImpl.fromJson;

  @override
  List<ProductionItemModel>? get items;
  @override
  int? get totalItems;

  /// Create a copy of ProductionItemsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductionItemsResponseImplCopyWith<_$ProductionItemsResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProductionTaskCancelResponse _$ProductionTaskCancelResponseFromJson(
    Map<String, dynamic> json) {
  return _ProductionTaskCancelResponse.fromJson(json);
}

/// @nodoc
mixin _$ProductionTaskCancelResponse {
  bool? get success => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;

  /// Serializes this ProductionTaskCancelResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductionTaskCancelResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductionTaskCancelResponseCopyWith<ProductionTaskCancelResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductionTaskCancelResponseCopyWith<$Res> {
  factory $ProductionTaskCancelResponseCopyWith(
          ProductionTaskCancelResponse value,
          $Res Function(ProductionTaskCancelResponse) then) =
      _$ProductionTaskCancelResponseCopyWithImpl<$Res,
          ProductionTaskCancelResponse>;
  @useResult
  $Res call({bool? success, String? message});
}

/// @nodoc
class _$ProductionTaskCancelResponseCopyWithImpl<$Res,
        $Val extends ProductionTaskCancelResponse>
    implements $ProductionTaskCancelResponseCopyWith<$Res> {
  _$ProductionTaskCancelResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductionTaskCancelResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = freezed,
    Object? message = freezed,
  }) {
    return _then(_value.copyWith(
      success: freezed == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool?,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductionTaskCancelResponseImplCopyWith<$Res>
    implements $ProductionTaskCancelResponseCopyWith<$Res> {
  factory _$$ProductionTaskCancelResponseImplCopyWith(
          _$ProductionTaskCancelResponseImpl value,
          $Res Function(_$ProductionTaskCancelResponseImpl) then) =
      __$$ProductionTaskCancelResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool? success, String? message});
}

/// @nodoc
class __$$ProductionTaskCancelResponseImplCopyWithImpl<$Res>
    extends _$ProductionTaskCancelResponseCopyWithImpl<$Res,
        _$ProductionTaskCancelResponseImpl>
    implements _$$ProductionTaskCancelResponseImplCopyWith<$Res> {
  __$$ProductionTaskCancelResponseImplCopyWithImpl(
      _$ProductionTaskCancelResponseImpl _value,
      $Res Function(_$ProductionTaskCancelResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductionTaskCancelResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = freezed,
    Object? message = freezed,
  }) {
    return _then(_$ProductionTaskCancelResponseImpl(
      success: freezed == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool?,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProductionTaskCancelResponseImpl
    implements _ProductionTaskCancelResponse {
  const _$ProductionTaskCancelResponseImpl({this.success, this.message});

  factory _$ProductionTaskCancelResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$ProductionTaskCancelResponseImplFromJson(json);

  @override
  final bool? success;
  @override
  final String? message;

  @override
  String toString() {
    return 'ProductionTaskCancelResponse(success: $success, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductionTaskCancelResponseImpl &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, success, message);

  /// Create a copy of ProductionTaskCancelResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductionTaskCancelResponseImplCopyWith<
          _$ProductionTaskCancelResponseImpl>
      get copyWith => __$$ProductionTaskCancelResponseImplCopyWithImpl<
          _$ProductionTaskCancelResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductionTaskCancelResponseImplToJson(
      this,
    );
  }
}

abstract class _ProductionTaskCancelResponse
    implements ProductionTaskCancelResponse {
  const factory _ProductionTaskCancelResponse(
      {final bool? success,
      final String? message}) = _$ProductionTaskCancelResponseImpl;

  factory _ProductionTaskCancelResponse.fromJson(Map<String, dynamic> json) =
      _$ProductionTaskCancelResponseImpl.fromJson;

  @override
  bool? get success;
  @override
  String? get message;

  /// Create a copy of ProductionTaskCancelResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductionTaskCancelResponseImplCopyWith<
          _$ProductionTaskCancelResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProductionTasksResponse _$ProductionTasksResponseFromJson(
    Map<String, dynamic> json) {
  return _ProductionTasksResponse.fromJson(json);
}

/// @nodoc
mixin _$ProductionTasksResponse {
  List<ProductionTaskModel>? get items => throw _privateConstructorUsedError;
  int? get totalItems => throw _privateConstructorUsedError;

  /// Serializes this ProductionTasksResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductionTasksResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductionTasksResponseCopyWith<ProductionTasksResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductionTasksResponseCopyWith<$Res> {
  factory $ProductionTasksResponseCopyWith(ProductionTasksResponse value,
          $Res Function(ProductionTasksResponse) then) =
      _$ProductionTasksResponseCopyWithImpl<$Res, ProductionTasksResponse>;
  @useResult
  $Res call({List<ProductionTaskModel>? items, int? totalItems});
}

/// @nodoc
class _$ProductionTasksResponseCopyWithImpl<$Res,
        $Val extends ProductionTasksResponse>
    implements $ProductionTasksResponseCopyWith<$Res> {
  _$ProductionTasksResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductionTasksResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = freezed,
    Object? totalItems = freezed,
  }) {
    return _then(_value.copyWith(
      items: freezed == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ProductionTaskModel>?,
      totalItems: freezed == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductionTasksResponseImplCopyWith<$Res>
    implements $ProductionTasksResponseCopyWith<$Res> {
  factory _$$ProductionTasksResponseImplCopyWith(
          _$ProductionTasksResponseImpl value,
          $Res Function(_$ProductionTasksResponseImpl) then) =
      __$$ProductionTasksResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<ProductionTaskModel>? items, int? totalItems});
}

/// @nodoc
class __$$ProductionTasksResponseImplCopyWithImpl<$Res>
    extends _$ProductionTasksResponseCopyWithImpl<$Res,
        _$ProductionTasksResponseImpl>
    implements _$$ProductionTasksResponseImplCopyWith<$Res> {
  __$$ProductionTasksResponseImplCopyWithImpl(
      _$ProductionTasksResponseImpl _value,
      $Res Function(_$ProductionTasksResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductionTasksResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = freezed,
    Object? totalItems = freezed,
  }) {
    return _then(_$ProductionTasksResponseImpl(
      items: freezed == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ProductionTaskModel>?,
      totalItems: freezed == totalItems
          ? _value.totalItems
          : totalItems // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProductionTasksResponseImpl implements _ProductionTasksResponse {
  const _$ProductionTasksResponseImpl(
      {final List<ProductionTaskModel>? items, this.totalItems})
      : _items = items;

  factory _$ProductionTasksResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductionTasksResponseImplFromJson(json);

  final List<ProductionTaskModel>? _items;
  @override
  List<ProductionTaskModel>? get items {
    final value = _items;
    if (value == null) return null;
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final int? totalItems;

  @override
  String toString() {
    return 'ProductionTasksResponse(items: $items, totalItems: $totalItems)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductionTasksResponseImpl &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            (identical(other.totalItems, totalItems) ||
                other.totalItems == totalItems));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_items), totalItems);

  /// Create a copy of ProductionTasksResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductionTasksResponseImplCopyWith<_$ProductionTasksResponseImpl>
      get copyWith => __$$ProductionTasksResponseImplCopyWithImpl<
          _$ProductionTasksResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductionTasksResponseImplToJson(
      this,
    );
  }
}

abstract class _ProductionTasksResponse implements ProductionTasksResponse {
  const factory _ProductionTasksResponse(
      {final List<ProductionTaskModel>? items,
      final int? totalItems}) = _$ProductionTasksResponseImpl;

  factory _ProductionTasksResponse.fromJson(Map<String, dynamic> json) =
      _$ProductionTasksResponseImpl.fromJson;

  @override
  List<ProductionTaskModel>? get items;
  @override
  int? get totalItems;

  /// Create a copy of ProductionTasksResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductionTasksResponseImplCopyWith<_$ProductionTasksResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProductionItemModel _$ProductionItemModelFromJson(Map<String, dynamic> json) {
  return _ProductionItemModel.fromJson(json);
}

/// @nodoc
mixin _$ProductionItemModel {
  String? get productId => throw _privateConstructorUsedError;
  ProductModel? get product => throw _privateConstructorUsedError;
  double? get requiredQuantity => throw _privateConstructorUsedError;
  double? get readyQuantity => throw _privateConstructorUsedError;
  ProductionStatus? get status => throw _privateConstructorUsedError;
  double? get filledPercentage => throw _privateConstructorUsedError;
  List<ProductionMaterialModel>? get materials =>
      throw _privateConstructorUsedError;
  ProductionTaskModel? get productionTask => throw _privateConstructorUsedError;

  /// Serializes this ProductionItemModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductionItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductionItemModelCopyWith<ProductionItemModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductionItemModelCopyWith<$Res> {
  factory $ProductionItemModelCopyWith(
          ProductionItemModel value, $Res Function(ProductionItemModel) then) =
      _$ProductionItemModelCopyWithImpl<$Res, ProductionItemModel>;
  @useResult
  $Res call(
      {String? productId,
      ProductModel? product,
      double? requiredQuantity,
      double? readyQuantity,
      ProductionStatus? status,
      double? filledPercentage,
      List<ProductionMaterialModel>? materials,
      ProductionTaskModel? productionTask});

  $ProductModelCopyWith<$Res>? get product;
  $ProductionTaskModelCopyWith<$Res>? get productionTask;
}

/// @nodoc
class _$ProductionItemModelCopyWithImpl<$Res, $Val extends ProductionItemModel>
    implements $ProductionItemModelCopyWith<$Res> {
  _$ProductionItemModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductionItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? product = freezed,
    Object? requiredQuantity = freezed,
    Object? readyQuantity = freezed,
    Object? status = freezed,
    Object? filledPercentage = freezed,
    Object? materials = freezed,
    Object? productionTask = freezed,
  }) {
    return _then(_value.copyWith(
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      product: freezed == product
          ? _value.product
          : product // ignore: cast_nullable_to_non_nullable
              as ProductModel?,
      requiredQuantity: freezed == requiredQuantity
          ? _value.requiredQuantity
          : requiredQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      readyQuantity: freezed == readyQuantity
          ? _value.readyQuantity
          : readyQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ProductionStatus?,
      filledPercentage: freezed == filledPercentage
          ? _value.filledPercentage
          : filledPercentage // ignore: cast_nullable_to_non_nullable
              as double?,
      materials: freezed == materials
          ? _value.materials
          : materials // ignore: cast_nullable_to_non_nullable
              as List<ProductionMaterialModel>?,
      productionTask: freezed == productionTask
          ? _value.productionTask
          : productionTask // ignore: cast_nullable_to_non_nullable
              as ProductionTaskModel?,
    ) as $Val);
  }

  /// Create a copy of ProductionItemModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProductModelCopyWith<$Res>? get product {
    if (_value.product == null) {
      return null;
    }

    return $ProductModelCopyWith<$Res>(_value.product!, (value) {
      return _then(_value.copyWith(product: value) as $Val);
    });
  }

  /// Create a copy of ProductionItemModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProductionTaskModelCopyWith<$Res>? get productionTask {
    if (_value.productionTask == null) {
      return null;
    }

    return $ProductionTaskModelCopyWith<$Res>(_value.productionTask!, (value) {
      return _then(_value.copyWith(productionTask: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ProductionItemModelImplCopyWith<$Res>
    implements $ProductionItemModelCopyWith<$Res> {
  factory _$$ProductionItemModelImplCopyWith(_$ProductionItemModelImpl value,
          $Res Function(_$ProductionItemModelImpl) then) =
      __$$ProductionItemModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? productId,
      ProductModel? product,
      double? requiredQuantity,
      double? readyQuantity,
      ProductionStatus? status,
      double? filledPercentage,
      List<ProductionMaterialModel>? materials,
      ProductionTaskModel? productionTask});

  @override
  $ProductModelCopyWith<$Res>? get product;
  @override
  $ProductionTaskModelCopyWith<$Res>? get productionTask;
}

/// @nodoc
class __$$ProductionItemModelImplCopyWithImpl<$Res>
    extends _$ProductionItemModelCopyWithImpl<$Res, _$ProductionItemModelImpl>
    implements _$$ProductionItemModelImplCopyWith<$Res> {
  __$$ProductionItemModelImplCopyWithImpl(_$ProductionItemModelImpl _value,
      $Res Function(_$ProductionItemModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductionItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? product = freezed,
    Object? requiredQuantity = freezed,
    Object? readyQuantity = freezed,
    Object? status = freezed,
    Object? filledPercentage = freezed,
    Object? materials = freezed,
    Object? productionTask = freezed,
  }) {
    return _then(_$ProductionItemModelImpl(
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      product: freezed == product
          ? _value.product
          : product // ignore: cast_nullable_to_non_nullable
              as ProductModel?,
      requiredQuantity: freezed == requiredQuantity
          ? _value.requiredQuantity
          : requiredQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      readyQuantity: freezed == readyQuantity
          ? _value.readyQuantity
          : readyQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ProductionStatus?,
      filledPercentage: freezed == filledPercentage
          ? _value.filledPercentage
          : filledPercentage // ignore: cast_nullable_to_non_nullable
              as double?,
      materials: freezed == materials
          ? _value._materials
          : materials // ignore: cast_nullable_to_non_nullable
              as List<ProductionMaterialModel>?,
      productionTask: freezed == productionTask
          ? _value.productionTask
          : productionTask // ignore: cast_nullable_to_non_nullable
              as ProductionTaskModel?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProductionItemModelImpl implements _ProductionItemModel {
  const _$ProductionItemModelImpl(
      {this.productId,
      this.product,
      this.requiredQuantity,
      this.readyQuantity,
      this.status,
      this.filledPercentage,
      final List<ProductionMaterialModel>? materials,
      this.productionTask})
      : _materials = materials;

  factory _$ProductionItemModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductionItemModelImplFromJson(json);

  @override
  final String? productId;
  @override
  final ProductModel? product;
  @override
  final double? requiredQuantity;
  @override
  final double? readyQuantity;
  @override
  final ProductionStatus? status;
  @override
  final double? filledPercentage;
  final List<ProductionMaterialModel>? _materials;
  @override
  List<ProductionMaterialModel>? get materials {
    final value = _materials;
    if (value == null) return null;
    if (_materials is EqualUnmodifiableListView) return _materials;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final ProductionTaskModel? productionTask;

  @override
  String toString() {
    return 'ProductionItemModel(productId: $productId, product: $product, requiredQuantity: $requiredQuantity, readyQuantity: $readyQuantity, status: $status, filledPercentage: $filledPercentage, materials: $materials, productionTask: $productionTask)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductionItemModelImpl &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.product, product) || other.product == product) &&
            (identical(other.requiredQuantity, requiredQuantity) ||
                other.requiredQuantity == requiredQuantity) &&
            (identical(other.readyQuantity, readyQuantity) ||
                other.readyQuantity == readyQuantity) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.filledPercentage, filledPercentage) ||
                other.filledPercentage == filledPercentage) &&
            const DeepCollectionEquality()
                .equals(other._materials, _materials) &&
            (identical(other.productionTask, productionTask) ||
                other.productionTask == productionTask));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      productId,
      product,
      requiredQuantity,
      readyQuantity,
      status,
      filledPercentage,
      const DeepCollectionEquality().hash(_materials),
      productionTask);

  /// Create a copy of ProductionItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductionItemModelImplCopyWith<_$ProductionItemModelImpl> get copyWith =>
      __$$ProductionItemModelImplCopyWithImpl<_$ProductionItemModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductionItemModelImplToJson(
      this,
    );
  }
}

abstract class _ProductionItemModel implements ProductionItemModel {
  const factory _ProductionItemModel(
      {final String? productId,
      final ProductModel? product,
      final double? requiredQuantity,
      final double? readyQuantity,
      final ProductionStatus? status,
      final double? filledPercentage,
      final List<ProductionMaterialModel>? materials,
      final ProductionTaskModel? productionTask}) = _$ProductionItemModelImpl;

  factory _ProductionItemModel.fromJson(Map<String, dynamic> json) =
      _$ProductionItemModelImpl.fromJson;

  @override
  String? get productId;
  @override
  ProductModel? get product;
  @override
  double? get requiredQuantity;
  @override
  double? get readyQuantity;
  @override
  ProductionStatus? get status;
  @override
  double? get filledPercentage;
  @override
  List<ProductionMaterialModel>? get materials;
  @override
  ProductionTaskModel? get productionTask;

  /// Create a copy of ProductionItemModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductionItemModelImplCopyWith<_$ProductionItemModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProductionMaterialModel _$ProductionMaterialModelFromJson(
    Map<String, dynamic> json) {
  return _ProductionMaterialModel.fromJson(json);
}

/// @nodoc
mixin _$ProductionMaterialModel {
  String? get materialId => throw _privateConstructorUsedError;
  NomenclatureModel? get material => throw _privateConstructorUsedError;
  String? get materialRequirements => throw _privateConstructorUsedError;
  double? get required => throw _privateConstructorUsedError;
  double? get available => throw _privateConstructorUsedError;
  double? get filledPercentage => throw _privateConstructorUsedError;
  MaterialModel? get storageItem => throw _privateConstructorUsedError;

  /// Serializes this ProductionMaterialModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductionMaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductionMaterialModelCopyWith<ProductionMaterialModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductionMaterialModelCopyWith<$Res> {
  factory $ProductionMaterialModelCopyWith(ProductionMaterialModel value,
          $Res Function(ProductionMaterialModel) then) =
      _$ProductionMaterialModelCopyWithImpl<$Res, ProductionMaterialModel>;
  @useResult
  $Res call(
      {String? materialId,
      NomenclatureModel? material,
      String? materialRequirements,
      double? required,
      double? available,
      double? filledPercentage,
      MaterialModel? storageItem});

  $MaterialModelCopyWith<$Res>? get storageItem;
}

/// @nodoc
class _$ProductionMaterialModelCopyWithImpl<$Res,
        $Val extends ProductionMaterialModel>
    implements $ProductionMaterialModelCopyWith<$Res> {
  _$ProductionMaterialModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductionMaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? materialId = freezed,
    Object? material = freezed,
    Object? materialRequirements = freezed,
    Object? required = freezed,
    Object? available = freezed,
    Object? filledPercentage = freezed,
    Object? storageItem = freezed,
  }) {
    return _then(_value.copyWith(
      materialId: freezed == materialId
          ? _value.materialId
          : materialId // ignore: cast_nullable_to_non_nullable
              as String?,
      material: freezed == material
          ? _value.material
          : material // ignore: cast_nullable_to_non_nullable
              as NomenclatureModel?,
      materialRequirements: freezed == materialRequirements
          ? _value.materialRequirements
          : materialRequirements // ignore: cast_nullable_to_non_nullable
              as String?,
      required: freezed == required
          ? _value.required
          : required // ignore: cast_nullable_to_non_nullable
              as double?,
      available: freezed == available
          ? _value.available
          : available // ignore: cast_nullable_to_non_nullable
              as double?,
      filledPercentage: freezed == filledPercentage
          ? _value.filledPercentage
          : filledPercentage // ignore: cast_nullable_to_non_nullable
              as double?,
      storageItem: freezed == storageItem
          ? _value.storageItem
          : storageItem // ignore: cast_nullable_to_non_nullable
              as MaterialModel?,
    ) as $Val);
  }

  /// Create a copy of ProductionMaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MaterialModelCopyWith<$Res>? get storageItem {
    if (_value.storageItem == null) {
      return null;
    }

    return $MaterialModelCopyWith<$Res>(_value.storageItem!, (value) {
      return _then(_value.copyWith(storageItem: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ProductionMaterialModelImplCopyWith<$Res>
    implements $ProductionMaterialModelCopyWith<$Res> {
  factory _$$ProductionMaterialModelImplCopyWith(
          _$ProductionMaterialModelImpl value,
          $Res Function(_$ProductionMaterialModelImpl) then) =
      __$$ProductionMaterialModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? materialId,
      NomenclatureModel? material,
      String? materialRequirements,
      double? required,
      double? available,
      double? filledPercentage,
      MaterialModel? storageItem});

  @override
  $MaterialModelCopyWith<$Res>? get storageItem;
}

/// @nodoc
class __$$ProductionMaterialModelImplCopyWithImpl<$Res>
    extends _$ProductionMaterialModelCopyWithImpl<$Res,
        _$ProductionMaterialModelImpl>
    implements _$$ProductionMaterialModelImplCopyWith<$Res> {
  __$$ProductionMaterialModelImplCopyWithImpl(
      _$ProductionMaterialModelImpl _value,
      $Res Function(_$ProductionMaterialModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductionMaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? materialId = freezed,
    Object? material = freezed,
    Object? materialRequirements = freezed,
    Object? required = freezed,
    Object? available = freezed,
    Object? filledPercentage = freezed,
    Object? storageItem = freezed,
  }) {
    return _then(_$ProductionMaterialModelImpl(
      materialId: freezed == materialId
          ? _value.materialId
          : materialId // ignore: cast_nullable_to_non_nullable
              as String?,
      material: freezed == material
          ? _value.material
          : material // ignore: cast_nullable_to_non_nullable
              as NomenclatureModel?,
      materialRequirements: freezed == materialRequirements
          ? _value.materialRequirements
          : materialRequirements // ignore: cast_nullable_to_non_nullable
              as String?,
      required: freezed == required
          ? _value.required
          : required // ignore: cast_nullable_to_non_nullable
              as double?,
      available: freezed == available
          ? _value.available
          : available // ignore: cast_nullable_to_non_nullable
              as double?,
      filledPercentage: freezed == filledPercentage
          ? _value.filledPercentage
          : filledPercentage // ignore: cast_nullable_to_non_nullable
              as double?,
      storageItem: freezed == storageItem
          ? _value.storageItem
          : storageItem // ignore: cast_nullable_to_non_nullable
              as MaterialModel?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProductionMaterialModelImpl implements _ProductionMaterialModel {
  const _$ProductionMaterialModelImpl(
      {this.materialId,
      this.material,
      this.materialRequirements,
      this.required,
      this.available,
      this.filledPercentage,
      this.storageItem});

  factory _$ProductionMaterialModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductionMaterialModelImplFromJson(json);

  @override
  final String? materialId;
  @override
  final NomenclatureModel? material;
  @override
  final String? materialRequirements;
  @override
  final double? required;
  @override
  final double? available;
  @override
  final double? filledPercentage;
  @override
  final MaterialModel? storageItem;

  @override
  String toString() {
    return 'ProductionMaterialModel(materialId: $materialId, material: $material, materialRequirements: $materialRequirements, required: $required, available: $available, filledPercentage: $filledPercentage, storageItem: $storageItem)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductionMaterialModelImpl &&
            (identical(other.materialId, materialId) ||
                other.materialId == materialId) &&
            (identical(other.material, material) ||
                other.material == material) &&
            (identical(other.materialRequirements, materialRequirements) ||
                other.materialRequirements == materialRequirements) &&
            (identical(other.required, required) ||
                other.required == required) &&
            (identical(other.available, available) ||
                other.available == available) &&
            (identical(other.filledPercentage, filledPercentage) ||
                other.filledPercentage == filledPercentage) &&
            (identical(other.storageItem, storageItem) ||
                other.storageItem == storageItem));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, materialId, material,
      materialRequirements, required, available, filledPercentage, storageItem);

  /// Create a copy of ProductionMaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductionMaterialModelImplCopyWith<_$ProductionMaterialModelImpl>
      get copyWith => __$$ProductionMaterialModelImplCopyWithImpl<
          _$ProductionMaterialModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductionMaterialModelImplToJson(
      this,
    );
  }
}

abstract class _ProductionMaterialModel implements ProductionMaterialModel {
  const factory _ProductionMaterialModel(
      {final String? materialId,
      final NomenclatureModel? material,
      final String? materialRequirements,
      final double? required,
      final double? available,
      final double? filledPercentage,
      final MaterialModel? storageItem}) = _$ProductionMaterialModelImpl;

  factory _ProductionMaterialModel.fromJson(Map<String, dynamic> json) =
      _$ProductionMaterialModelImpl.fromJson;

  @override
  String? get materialId;
  @override
  NomenclatureModel? get material;
  @override
  String? get materialRequirements;
  @override
  double? get required;
  @override
  double? get available;
  @override
  double? get filledPercentage;
  @override
  MaterialModel? get storageItem;

  /// Create a copy of ProductionMaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductionMaterialModelImplCopyWith<_$ProductionMaterialModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProductionTaskModel _$ProductionTaskModelFromJson(Map<String, dynamic> json) {
  return _ProductionTaskModel.fromJson(json);
}

/// @nodoc
mixin _$ProductionTaskModel {
  @JsonKey(name: '_id')
  String? get id => throw _privateConstructorUsedError;
  String? get projectId => throw _privateConstructorUsedError;
  String? get productId => throw _privateConstructorUsedError;
  ProductionTaskStatus? get status => throw _privateConstructorUsedError;
  double? get quantityRequired => throw _privateConstructorUsedError;
  List<ProductionTaskMaterialModel>? get materials =>
      throw _privateConstructorUsedError;
  List<ProductionTaskStatusHistoryModel>? get statusHistory =>
      throw _privateConstructorUsedError;
  String? get requestedBy => throw _privateConstructorUsedError;
  String? get issuedBy => throw _privateConstructorUsedError;
  String? get assembledBy => throw _privateConstructorUsedError;
  String? get inspectedBy => throw _privateConstructorUsedError;
  bool? get cancelled => throw _privateConstructorUsedError;
  DateTime? get cancelledAt => throw _privateConstructorUsedError;
  String? get cancelledBy => throw _privateConstructorUsedError;
  String? get cancelReason => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this ProductionTaskModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductionTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductionTaskModelCopyWith<ProductionTaskModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductionTaskModelCopyWith<$Res> {
  factory $ProductionTaskModelCopyWith(
          ProductionTaskModel value, $Res Function(ProductionTaskModel) then) =
      _$ProductionTaskModelCopyWithImpl<$Res, ProductionTaskModel>;
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      String? projectId,
      String? productId,
      ProductionTaskStatus? status,
      double? quantityRequired,
      List<ProductionTaskMaterialModel>? materials,
      List<ProductionTaskStatusHistoryModel>? statusHistory,
      String? requestedBy,
      String? issuedBy,
      String? assembledBy,
      String? inspectedBy,
      bool? cancelled,
      DateTime? cancelledAt,
      String? cancelledBy,
      String? cancelReason,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class _$ProductionTaskModelCopyWithImpl<$Res, $Val extends ProductionTaskModel>
    implements $ProductionTaskModelCopyWith<$Res> {
  _$ProductionTaskModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductionTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? projectId = freezed,
    Object? productId = freezed,
    Object? status = freezed,
    Object? quantityRequired = freezed,
    Object? materials = freezed,
    Object? statusHistory = freezed,
    Object? requestedBy = freezed,
    Object? issuedBy = freezed,
    Object? assembledBy = freezed,
    Object? inspectedBy = freezed,
    Object? cancelled = freezed,
    Object? cancelledAt = freezed,
    Object? cancelledBy = freezed,
    Object? cancelReason = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ProductionTaskStatus?,
      quantityRequired: freezed == quantityRequired
          ? _value.quantityRequired
          : quantityRequired // ignore: cast_nullable_to_non_nullable
              as double?,
      materials: freezed == materials
          ? _value.materials
          : materials // ignore: cast_nullable_to_non_nullable
              as List<ProductionTaskMaterialModel>?,
      statusHistory: freezed == statusHistory
          ? _value.statusHistory
          : statusHistory // ignore: cast_nullable_to_non_nullable
              as List<ProductionTaskStatusHistoryModel>?,
      requestedBy: freezed == requestedBy
          ? _value.requestedBy
          : requestedBy // ignore: cast_nullable_to_non_nullable
              as String?,
      issuedBy: freezed == issuedBy
          ? _value.issuedBy
          : issuedBy // ignore: cast_nullable_to_non_nullable
              as String?,
      assembledBy: freezed == assembledBy
          ? _value.assembledBy
          : assembledBy // ignore: cast_nullable_to_non_nullable
              as String?,
      inspectedBy: freezed == inspectedBy
          ? _value.inspectedBy
          : inspectedBy // ignore: cast_nullable_to_non_nullable
              as String?,
      cancelled: freezed == cancelled
          ? _value.cancelled
          : cancelled // ignore: cast_nullable_to_non_nullable
              as bool?,
      cancelledAt: freezed == cancelledAt
          ? _value.cancelledAt
          : cancelledAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      cancelledBy: freezed == cancelledBy
          ? _value.cancelledBy
          : cancelledBy // ignore: cast_nullable_to_non_nullable
              as String?,
      cancelReason: freezed == cancelReason
          ? _value.cancelReason
          : cancelReason // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductionTaskModelImplCopyWith<$Res>
    implements $ProductionTaskModelCopyWith<$Res> {
  factory _$$ProductionTaskModelImplCopyWith(_$ProductionTaskModelImpl value,
          $Res Function(_$ProductionTaskModelImpl) then) =
      __$$ProductionTaskModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '_id') String? id,
      String? projectId,
      String? productId,
      ProductionTaskStatus? status,
      double? quantityRequired,
      List<ProductionTaskMaterialModel>? materials,
      List<ProductionTaskStatusHistoryModel>? statusHistory,
      String? requestedBy,
      String? issuedBy,
      String? assembledBy,
      String? inspectedBy,
      bool? cancelled,
      DateTime? cancelledAt,
      String? cancelledBy,
      String? cancelReason,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class __$$ProductionTaskModelImplCopyWithImpl<$Res>
    extends _$ProductionTaskModelCopyWithImpl<$Res, _$ProductionTaskModelImpl>
    implements _$$ProductionTaskModelImplCopyWith<$Res> {
  __$$ProductionTaskModelImplCopyWithImpl(_$ProductionTaskModelImpl _value,
      $Res Function(_$ProductionTaskModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductionTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? projectId = freezed,
    Object? productId = freezed,
    Object? status = freezed,
    Object? quantityRequired = freezed,
    Object? materials = freezed,
    Object? statusHistory = freezed,
    Object? requestedBy = freezed,
    Object? issuedBy = freezed,
    Object? assembledBy = freezed,
    Object? inspectedBy = freezed,
    Object? cancelled = freezed,
    Object? cancelledAt = freezed,
    Object? cancelledBy = freezed,
    Object? cancelReason = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$ProductionTaskModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ProductionTaskStatus?,
      quantityRequired: freezed == quantityRequired
          ? _value.quantityRequired
          : quantityRequired // ignore: cast_nullable_to_non_nullable
              as double?,
      materials: freezed == materials
          ? _value._materials
          : materials // ignore: cast_nullable_to_non_nullable
              as List<ProductionTaskMaterialModel>?,
      statusHistory: freezed == statusHistory
          ? _value._statusHistory
          : statusHistory // ignore: cast_nullable_to_non_nullable
              as List<ProductionTaskStatusHistoryModel>?,
      requestedBy: freezed == requestedBy
          ? _value.requestedBy
          : requestedBy // ignore: cast_nullable_to_non_nullable
              as String?,
      issuedBy: freezed == issuedBy
          ? _value.issuedBy
          : issuedBy // ignore: cast_nullable_to_non_nullable
              as String?,
      assembledBy: freezed == assembledBy
          ? _value.assembledBy
          : assembledBy // ignore: cast_nullable_to_non_nullable
              as String?,
      inspectedBy: freezed == inspectedBy
          ? _value.inspectedBy
          : inspectedBy // ignore: cast_nullable_to_non_nullable
              as String?,
      cancelled: freezed == cancelled
          ? _value.cancelled
          : cancelled // ignore: cast_nullable_to_non_nullable
              as bool?,
      cancelledAt: freezed == cancelledAt
          ? _value.cancelledAt
          : cancelledAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      cancelledBy: freezed == cancelledBy
          ? _value.cancelledBy
          : cancelledBy // ignore: cast_nullable_to_non_nullable
              as String?,
      cancelReason: freezed == cancelReason
          ? _value.cancelReason
          : cancelReason // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProductionTaskModelImpl implements _ProductionTaskModel {
  const _$ProductionTaskModelImpl(
      {@JsonKey(name: '_id') this.id,
      this.projectId,
      this.productId,
      this.status,
      this.quantityRequired,
      final List<ProductionTaskMaterialModel>? materials,
      final List<ProductionTaskStatusHistoryModel>? statusHistory,
      this.requestedBy,
      this.issuedBy,
      this.assembledBy,
      this.inspectedBy,
      this.cancelled,
      this.cancelledAt,
      this.cancelledBy,
      this.cancelReason,
      this.createdAt,
      this.updatedAt})
      : _materials = materials,
        _statusHistory = statusHistory;

  factory _$ProductionTaskModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductionTaskModelImplFromJson(json);

  @override
  @JsonKey(name: '_id')
  final String? id;
  @override
  final String? projectId;
  @override
  final String? productId;
  @override
  final ProductionTaskStatus? status;
  @override
  final double? quantityRequired;
  final List<ProductionTaskMaterialModel>? _materials;
  @override
  List<ProductionTaskMaterialModel>? get materials {
    final value = _materials;
    if (value == null) return null;
    if (_materials is EqualUnmodifiableListView) return _materials;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ProductionTaskStatusHistoryModel>? _statusHistory;
  @override
  List<ProductionTaskStatusHistoryModel>? get statusHistory {
    final value = _statusHistory;
    if (value == null) return null;
    if (_statusHistory is EqualUnmodifiableListView) return _statusHistory;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? requestedBy;
  @override
  final String? issuedBy;
  @override
  final String? assembledBy;
  @override
  final String? inspectedBy;
  @override
  final bool? cancelled;
  @override
  final DateTime? cancelledAt;
  @override
  final String? cancelledBy;
  @override
  final String? cancelReason;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'ProductionTaskModel(id: $id, projectId: $projectId, productId: $productId, status: $status, quantityRequired: $quantityRequired, materials: $materials, statusHistory: $statusHistory, requestedBy: $requestedBy, issuedBy: $issuedBy, assembledBy: $assembledBy, inspectedBy: $inspectedBy, cancelled: $cancelled, cancelledAt: $cancelledAt, cancelledBy: $cancelledBy, cancelReason: $cancelReason, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductionTaskModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.quantityRequired, quantityRequired) ||
                other.quantityRequired == quantityRequired) &&
            const DeepCollectionEquality()
                .equals(other._materials, _materials) &&
            const DeepCollectionEquality()
                .equals(other._statusHistory, _statusHistory) &&
            (identical(other.requestedBy, requestedBy) ||
                other.requestedBy == requestedBy) &&
            (identical(other.issuedBy, issuedBy) ||
                other.issuedBy == issuedBy) &&
            (identical(other.assembledBy, assembledBy) ||
                other.assembledBy == assembledBy) &&
            (identical(other.inspectedBy, inspectedBy) ||
                other.inspectedBy == inspectedBy) &&
            (identical(other.cancelled, cancelled) ||
                other.cancelled == cancelled) &&
            (identical(other.cancelledAt, cancelledAt) ||
                other.cancelledAt == cancelledAt) &&
            (identical(other.cancelledBy, cancelledBy) ||
                other.cancelledBy == cancelledBy) &&
            (identical(other.cancelReason, cancelReason) ||
                other.cancelReason == cancelReason) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      projectId,
      productId,
      status,
      quantityRequired,
      const DeepCollectionEquality().hash(_materials),
      const DeepCollectionEquality().hash(_statusHistory),
      requestedBy,
      issuedBy,
      assembledBy,
      inspectedBy,
      cancelled,
      cancelledAt,
      cancelledBy,
      cancelReason,
      createdAt,
      updatedAt);

  /// Create a copy of ProductionTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductionTaskModelImplCopyWith<_$ProductionTaskModelImpl> get copyWith =>
      __$$ProductionTaskModelImplCopyWithImpl<_$ProductionTaskModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductionTaskModelImplToJson(
      this,
    );
  }
}

abstract class _ProductionTaskModel implements ProductionTaskModel {
  const factory _ProductionTaskModel(
      {@JsonKey(name: '_id') final String? id,
      final String? projectId,
      final String? productId,
      final ProductionTaskStatus? status,
      final double? quantityRequired,
      final List<ProductionTaskMaterialModel>? materials,
      final List<ProductionTaskStatusHistoryModel>? statusHistory,
      final String? requestedBy,
      final String? issuedBy,
      final String? assembledBy,
      final String? inspectedBy,
      final bool? cancelled,
      final DateTime? cancelledAt,
      final String? cancelledBy,
      final String? cancelReason,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$ProductionTaskModelImpl;

  factory _ProductionTaskModel.fromJson(Map<String, dynamic> json) =
      _$ProductionTaskModelImpl.fromJson;

  @override
  @JsonKey(name: '_id')
  String? get id;
  @override
  String? get projectId;
  @override
  String? get productId;
  @override
  ProductionTaskStatus? get status;
  @override
  double? get quantityRequired;
  @override
  List<ProductionTaskMaterialModel>? get materials;
  @override
  List<ProductionTaskStatusHistoryModel>? get statusHistory;
  @override
  String? get requestedBy;
  @override
  String? get issuedBy;
  @override
  String? get assembledBy;
  @override
  String? get inspectedBy;
  @override
  bool? get cancelled;
  @override
  DateTime? get cancelledAt;
  @override
  String? get cancelledBy;
  @override
  String? get cancelReason;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of ProductionTaskModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductionTaskModelImplCopyWith<_$ProductionTaskModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProductionTaskMaterialModel _$ProductionTaskMaterialModelFromJson(
    Map<String, dynamic> json) {
  return _ProductionTaskMaterialModel.fromJson(json);
}

/// @nodoc
mixin _$ProductionTaskMaterialModel {
  String? get storageItemId => throw _privateConstructorUsedError;
  MaterialModel? get storageItem => throw _privateConstructorUsedError;
  double? get requiredQuantity => throw _privateConstructorUsedError;
  double? get issuedQuantity => throw _privateConstructorUsedError;
  String? get materialName => throw _privateConstructorUsedError;
  String? get materialUnit => throw _privateConstructorUsedError;

  /// Serializes this ProductionTaskMaterialModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductionTaskMaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductionTaskMaterialModelCopyWith<ProductionTaskMaterialModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductionTaskMaterialModelCopyWith<$Res> {
  factory $ProductionTaskMaterialModelCopyWith(
          ProductionTaskMaterialModel value,
          $Res Function(ProductionTaskMaterialModel) then) =
      _$ProductionTaskMaterialModelCopyWithImpl<$Res,
          ProductionTaskMaterialModel>;
  @useResult
  $Res call(
      {String? storageItemId,
      MaterialModel? storageItem,
      double? requiredQuantity,
      double? issuedQuantity,
      String? materialName,
      String? materialUnit});

  $MaterialModelCopyWith<$Res>? get storageItem;
}

/// @nodoc
class _$ProductionTaskMaterialModelCopyWithImpl<$Res,
        $Val extends ProductionTaskMaterialModel>
    implements $ProductionTaskMaterialModelCopyWith<$Res> {
  _$ProductionTaskMaterialModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductionTaskMaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? storageItemId = freezed,
    Object? storageItem = freezed,
    Object? requiredQuantity = freezed,
    Object? issuedQuantity = freezed,
    Object? materialName = freezed,
    Object? materialUnit = freezed,
  }) {
    return _then(_value.copyWith(
      storageItemId: freezed == storageItemId
          ? _value.storageItemId
          : storageItemId // ignore: cast_nullable_to_non_nullable
              as String?,
      storageItem: freezed == storageItem
          ? _value.storageItem
          : storageItem // ignore: cast_nullable_to_non_nullable
              as MaterialModel?,
      requiredQuantity: freezed == requiredQuantity
          ? _value.requiredQuantity
          : requiredQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      issuedQuantity: freezed == issuedQuantity
          ? _value.issuedQuantity
          : issuedQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      materialName: freezed == materialName
          ? _value.materialName
          : materialName // ignore: cast_nullable_to_non_nullable
              as String?,
      materialUnit: freezed == materialUnit
          ? _value.materialUnit
          : materialUnit // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of ProductionTaskMaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MaterialModelCopyWith<$Res>? get storageItem {
    if (_value.storageItem == null) {
      return null;
    }

    return $MaterialModelCopyWith<$Res>(_value.storageItem!, (value) {
      return _then(_value.copyWith(storageItem: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ProductionTaskMaterialModelImplCopyWith<$Res>
    implements $ProductionTaskMaterialModelCopyWith<$Res> {
  factory _$$ProductionTaskMaterialModelImplCopyWith(
          _$ProductionTaskMaterialModelImpl value,
          $Res Function(_$ProductionTaskMaterialModelImpl) then) =
      __$$ProductionTaskMaterialModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? storageItemId,
      MaterialModel? storageItem,
      double? requiredQuantity,
      double? issuedQuantity,
      String? materialName,
      String? materialUnit});

  @override
  $MaterialModelCopyWith<$Res>? get storageItem;
}

/// @nodoc
class __$$ProductionTaskMaterialModelImplCopyWithImpl<$Res>
    extends _$ProductionTaskMaterialModelCopyWithImpl<$Res,
        _$ProductionTaskMaterialModelImpl>
    implements _$$ProductionTaskMaterialModelImplCopyWith<$Res> {
  __$$ProductionTaskMaterialModelImplCopyWithImpl(
      _$ProductionTaskMaterialModelImpl _value,
      $Res Function(_$ProductionTaskMaterialModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductionTaskMaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? storageItemId = freezed,
    Object? storageItem = freezed,
    Object? requiredQuantity = freezed,
    Object? issuedQuantity = freezed,
    Object? materialName = freezed,
    Object? materialUnit = freezed,
  }) {
    return _then(_$ProductionTaskMaterialModelImpl(
      storageItemId: freezed == storageItemId
          ? _value.storageItemId
          : storageItemId // ignore: cast_nullable_to_non_nullable
              as String?,
      storageItem: freezed == storageItem
          ? _value.storageItem
          : storageItem // ignore: cast_nullable_to_non_nullable
              as MaterialModel?,
      requiredQuantity: freezed == requiredQuantity
          ? _value.requiredQuantity
          : requiredQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      issuedQuantity: freezed == issuedQuantity
          ? _value.issuedQuantity
          : issuedQuantity // ignore: cast_nullable_to_non_nullable
              as double?,
      materialName: freezed == materialName
          ? _value.materialName
          : materialName // ignore: cast_nullable_to_non_nullable
              as String?,
      materialUnit: freezed == materialUnit
          ? _value.materialUnit
          : materialUnit // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProductionTaskMaterialModelImpl
    implements _ProductionTaskMaterialModel {
  const _$ProductionTaskMaterialModelImpl(
      {this.storageItemId,
      this.storageItem,
      this.requiredQuantity,
      this.issuedQuantity,
      this.materialName,
      this.materialUnit});

  factory _$ProductionTaskMaterialModelImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$ProductionTaskMaterialModelImplFromJson(json);

  @override
  final String? storageItemId;
  @override
  final MaterialModel? storageItem;
  @override
  final double? requiredQuantity;
  @override
  final double? issuedQuantity;
  @override
  final String? materialName;
  @override
  final String? materialUnit;

  @override
  String toString() {
    return 'ProductionTaskMaterialModel(storageItemId: $storageItemId, storageItem: $storageItem, requiredQuantity: $requiredQuantity, issuedQuantity: $issuedQuantity, materialName: $materialName, materialUnit: $materialUnit)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductionTaskMaterialModelImpl &&
            (identical(other.storageItemId, storageItemId) ||
                other.storageItemId == storageItemId) &&
            (identical(other.storageItem, storageItem) ||
                other.storageItem == storageItem) &&
            (identical(other.requiredQuantity, requiredQuantity) ||
                other.requiredQuantity == requiredQuantity) &&
            (identical(other.issuedQuantity, issuedQuantity) ||
                other.issuedQuantity == issuedQuantity) &&
            (identical(other.materialName, materialName) ||
                other.materialName == materialName) &&
            (identical(other.materialUnit, materialUnit) ||
                other.materialUnit == materialUnit));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, storageItemId, storageItem,
      requiredQuantity, issuedQuantity, materialName, materialUnit);

  /// Create a copy of ProductionTaskMaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductionTaskMaterialModelImplCopyWith<_$ProductionTaskMaterialModelImpl>
      get copyWith => __$$ProductionTaskMaterialModelImplCopyWithImpl<
          _$ProductionTaskMaterialModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductionTaskMaterialModelImplToJson(
      this,
    );
  }
}

abstract class _ProductionTaskMaterialModel
    implements ProductionTaskMaterialModel {
  const factory _ProductionTaskMaterialModel(
      {final String? storageItemId,
      final MaterialModel? storageItem,
      final double? requiredQuantity,
      final double? issuedQuantity,
      final String? materialName,
      final String? materialUnit}) = _$ProductionTaskMaterialModelImpl;

  factory _ProductionTaskMaterialModel.fromJson(Map<String, dynamic> json) =
      _$ProductionTaskMaterialModelImpl.fromJson;

  @override
  String? get storageItemId;
  @override
  MaterialModel? get storageItem;
  @override
  double? get requiredQuantity;
  @override
  double? get issuedQuantity;
  @override
  String? get materialName;
  @override
  String? get materialUnit;

  /// Create a copy of ProductionTaskMaterialModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductionTaskMaterialModelImplCopyWith<_$ProductionTaskMaterialModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProductionTaskStatusHistoryModel _$ProductionTaskStatusHistoryModelFromJson(
    Map<String, dynamic> json) {
  return _ProductionTaskStatusHistoryModel.fromJson(json);
}

/// @nodoc
mixin _$ProductionTaskStatusHistoryModel {
  ProductionTaskStatus? get status => throw _privateConstructorUsedError;
  DateTime? get date => throw _privateConstructorUsedError;
  String? get userId => throw _privateConstructorUsedError;
  String? get comment => throw _privateConstructorUsedError;

  /// Serializes this ProductionTaskStatusHistoryModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductionTaskStatusHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductionTaskStatusHistoryModelCopyWith<ProductionTaskStatusHistoryModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductionTaskStatusHistoryModelCopyWith<$Res> {
  factory $ProductionTaskStatusHistoryModelCopyWith(
          ProductionTaskStatusHistoryModel value,
          $Res Function(ProductionTaskStatusHistoryModel) then) =
      _$ProductionTaskStatusHistoryModelCopyWithImpl<$Res,
          ProductionTaskStatusHistoryModel>;
  @useResult
  $Res call(
      {ProductionTaskStatus? status,
      DateTime? date,
      String? userId,
      String? comment});
}

/// @nodoc
class _$ProductionTaskStatusHistoryModelCopyWithImpl<$Res,
        $Val extends ProductionTaskStatusHistoryModel>
    implements $ProductionTaskStatusHistoryModelCopyWith<$Res> {
  _$ProductionTaskStatusHistoryModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductionTaskStatusHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = freezed,
    Object? date = freezed,
    Object? userId = freezed,
    Object? comment = freezed,
  }) {
    return _then(_value.copyWith(
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ProductionTaskStatus?,
      date: freezed == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductionTaskStatusHistoryModelImplCopyWith<$Res>
    implements $ProductionTaskStatusHistoryModelCopyWith<$Res> {
  factory _$$ProductionTaskStatusHistoryModelImplCopyWith(
          _$ProductionTaskStatusHistoryModelImpl value,
          $Res Function(_$ProductionTaskStatusHistoryModelImpl) then) =
      __$$ProductionTaskStatusHistoryModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {ProductionTaskStatus? status,
      DateTime? date,
      String? userId,
      String? comment});
}

/// @nodoc
class __$$ProductionTaskStatusHistoryModelImplCopyWithImpl<$Res>
    extends _$ProductionTaskStatusHistoryModelCopyWithImpl<$Res,
        _$ProductionTaskStatusHistoryModelImpl>
    implements _$$ProductionTaskStatusHistoryModelImplCopyWith<$Res> {
  __$$ProductionTaskStatusHistoryModelImplCopyWithImpl(
      _$ProductionTaskStatusHistoryModelImpl _value,
      $Res Function(_$ProductionTaskStatusHistoryModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductionTaskStatusHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = freezed,
    Object? date = freezed,
    Object? userId = freezed,
    Object? comment = freezed,
  }) {
    return _then(_$ProductionTaskStatusHistoryModelImpl(
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ProductionTaskStatus?,
      date: freezed == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(includeIfNull: false)
class _$ProductionTaskStatusHistoryModelImpl
    implements _ProductionTaskStatusHistoryModel {
  const _$ProductionTaskStatusHistoryModelImpl(
      {this.status, this.date, this.userId, this.comment});

  factory _$ProductionTaskStatusHistoryModelImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$ProductionTaskStatusHistoryModelImplFromJson(json);

  @override
  final ProductionTaskStatus? status;
  @override
  final DateTime? date;
  @override
  final String? userId;
  @override
  final String? comment;

  @override
  String toString() {
    return 'ProductionTaskStatusHistoryModel(status: $status, date: $date, userId: $userId, comment: $comment)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductionTaskStatusHistoryModelImpl &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.comment, comment) || other.comment == comment));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, status, date, userId, comment);

  /// Create a copy of ProductionTaskStatusHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductionTaskStatusHistoryModelImplCopyWith<
          _$ProductionTaskStatusHistoryModelImpl>
      get copyWith => __$$ProductionTaskStatusHistoryModelImplCopyWithImpl<
          _$ProductionTaskStatusHistoryModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductionTaskStatusHistoryModelImplToJson(
      this,
    );
  }
}

abstract class _ProductionTaskStatusHistoryModel
    implements ProductionTaskStatusHistoryModel {
  const factory _ProductionTaskStatusHistoryModel(
      {final ProductionTaskStatus? status,
      final DateTime? date,
      final String? userId,
      final String? comment}) = _$ProductionTaskStatusHistoryModelImpl;

  factory _ProductionTaskStatusHistoryModel.fromJson(
          Map<String, dynamic> json) =
      _$ProductionTaskStatusHistoryModelImpl.fromJson;

  @override
  ProductionTaskStatus? get status;
  @override
  DateTime? get date;
  @override
  String? get userId;
  @override
  String? get comment;

  /// Create a copy of ProductionTaskStatusHistoryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductionTaskStatusHistoryModelImplCopyWith<
          _$ProductionTaskStatusHistoryModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
