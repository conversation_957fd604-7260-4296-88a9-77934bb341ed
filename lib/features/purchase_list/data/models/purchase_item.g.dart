// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'purchase_item.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PurchaseItemModelImpl _$$PurchaseItemModelImplFromJson(
        Map<String, dynamic> json) =>
    _$PurchaseItemModelImpl(
      id: json['_id'] as String?,
      groupId: json['groupId'] as String?,
      name: json['name'] as String?,
      type: json['type'] as String?,
      contractor: json['contractor'] as String?,
      inputs: (json['inputs'] as List<dynamic>?)
          ?.map((e) => PurchaseInputModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      withPreferences: json['withPreferences'] as bool?,
      withChange: json['withChange'] as bool?,
      quantity: (json['quantity'] as num?)?.toDouble(),
      totalQuantity: (json['totalQuantity'] as num?)?.toDouble(),
      storageQuantity: (json['storageQuantity'] as num?)?.toDouble(),
      unitType: $enumDecodeNullable(_$UnitTypeEnumMap, json['unitType']),
      supplies: (json['supplies'] as List<dynamic>?)
          ?.map((e) => SupplyModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$PurchaseItemModelImplToJson(
        _$PurchaseItemModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) '_id': value,
      if (instance.groupId case final value?) 'groupId': value,
      if (instance.name case final value?) 'name': value,
      if (instance.type case final value?) 'type': value,
      if (instance.contractor case final value?) 'contractor': value,
      if (instance.inputs case final value?) 'inputs': value,
      if (instance.withPreferences case final value?) 'withPreferences': value,
      if (instance.withChange case final value?) 'withChange': value,
      if (instance.quantity case final value?) 'quantity': value,
      if (instance.totalQuantity case final value?) 'totalQuantity': value,
      if (instance.storageQuantity case final value?) 'storageQuantity': value,
      if (_$UnitTypeEnumMap[instance.unitType] case final value?)
        'unitType': value,
      if (instance.supplies case final value?) 'supplies': value,
    };

const _$UnitTypeEnumMap = {
  UnitType.kg: 'kg',
  UnitType.pcs: 'pcs',
  UnitType.m: 'm',
  UnitType.m2: 'm2',
  UnitType.m3: 'm3',
  UnitType.l: 'l',
};

_$PurchaseInputModelImpl _$$PurchaseInputModelImplFromJson(
        Map<String, dynamic> json) =>
    _$PurchaseInputModelImpl(
      id: json['_id'] as String?,
      name: json['name'] as String?,
      priority: (json['priority'] as num?)?.toInt(),
      quantity: (json['quantity'] as num?)?.toDouble(),
      totalQuantity: (json['totalQuantity'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$PurchaseInputModelImplToJson(
        _$PurchaseInputModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) '_id': value,
      if (instance.name case final value?) 'name': value,
      if (instance.priority case final value?) 'priority': value,
      if (instance.quantity case final value?) 'quantity': value,
      if (instance.totalQuantity case final value?) 'totalQuantity': value,
    };

_$PurchaseItemsModelImpl _$$PurchaseItemsModelImplFromJson(
        Map<String, dynamic> json) =>
    _$PurchaseItemsModelImpl(
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => PurchaseItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$PurchaseItemsModelImplToJson(
        _$PurchaseItemsModelImpl instance) =>
    <String, dynamic>{
      if (instance.items case final value?) 'items': value,
    };
