// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ProductionState {
// Data
  List<ProductionItemModel> get items => throw _privateConstructorUsedError;
  List<ProductionTaskModel> get tasks => throw _privateConstructorUsedError;
  ProductionTaskModel? get selectedTaskDetails =>
      throw _privateConstructorUsedError; // UI State
  ProductionView get currentView => throw _privateConstructorUsedError;
  Set<String> get selectedItemIds =>
      throw _privateConstructorUsedError; // Filters
  String? get projectId => throw _privateConstructorUsedError;
  ProductionTaskStatus? get statusFilter => throw _privateConstructorUsedError;
  DateTime? get dateFromFilter => throw _privateConstructorUsedError;
  DateTime? get dateToFilter =>
      throw _privateConstructorUsedError; // Loading States
  bool get isLoadingItems => throw _privateConstructorUsedError;
  bool get isLoadingTasks => throw _privateConstructorUsedError;
  bool get isLoadingTaskDetails => throw _privateConstructorUsedError;
  bool get isCreatingTask => throw _privateConstructorUsedError;
  bool get isCancellingTask => throw _privateConstructorUsedError;
  bool get isUpdatingStatus =>
      throw _privateConstructorUsedError; // Error States
  String? get itemsError => throw _privateConstructorUsedError;
  String? get tasksError => throw _privateConstructorUsedError;
  String? get taskDetailsError => throw _privateConstructorUsedError;
  String? get createTaskError => throw _privateConstructorUsedError;
  String? get cancelTaskError => throw _privateConstructorUsedError;
  String? get updateStatusError =>
      throw _privateConstructorUsedError; // Success Messages
  String? get successMessage => throw _privateConstructorUsedError;

  /// Create a copy of ProductionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductionStateCopyWith<ProductionState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductionStateCopyWith<$Res> {
  factory $ProductionStateCopyWith(
          ProductionState value, $Res Function(ProductionState) then) =
      _$ProductionStateCopyWithImpl<$Res, ProductionState>;
  @useResult
  $Res call(
      {List<ProductionItemModel> items,
      List<ProductionTaskModel> tasks,
      ProductionTaskModel? selectedTaskDetails,
      ProductionView currentView,
      Set<String> selectedItemIds,
      String? projectId,
      ProductionTaskStatus? statusFilter,
      DateTime? dateFromFilter,
      DateTime? dateToFilter,
      bool isLoadingItems,
      bool isLoadingTasks,
      bool isLoadingTaskDetails,
      bool isCreatingTask,
      bool isCancellingTask,
      bool isUpdatingStatus,
      String? itemsError,
      String? tasksError,
      String? taskDetailsError,
      String? createTaskError,
      String? cancelTaskError,
      String? updateStatusError,
      String? successMessage});

  $ProductionTaskModelCopyWith<$Res>? get selectedTaskDetails;
}

/// @nodoc
class _$ProductionStateCopyWithImpl<$Res, $Val extends ProductionState>
    implements $ProductionStateCopyWith<$Res> {
  _$ProductionStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductionState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = null,
    Object? tasks = null,
    Object? selectedTaskDetails = freezed,
    Object? currentView = null,
    Object? selectedItemIds = null,
    Object? projectId = freezed,
    Object? statusFilter = freezed,
    Object? dateFromFilter = freezed,
    Object? dateToFilter = freezed,
    Object? isLoadingItems = null,
    Object? isLoadingTasks = null,
    Object? isLoadingTaskDetails = null,
    Object? isCreatingTask = null,
    Object? isCancellingTask = null,
    Object? isUpdatingStatus = null,
    Object? itemsError = freezed,
    Object? tasksError = freezed,
    Object? taskDetailsError = freezed,
    Object? createTaskError = freezed,
    Object? cancelTaskError = freezed,
    Object? updateStatusError = freezed,
    Object? successMessage = freezed,
  }) {
    return _then(_value.copyWith(
      items: null == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ProductionItemModel>,
      tasks: null == tasks
          ? _value.tasks
          : tasks // ignore: cast_nullable_to_non_nullable
              as List<ProductionTaskModel>,
      selectedTaskDetails: freezed == selectedTaskDetails
          ? _value.selectedTaskDetails
          : selectedTaskDetails // ignore: cast_nullable_to_non_nullable
              as ProductionTaskModel?,
      currentView: null == currentView
          ? _value.currentView
          : currentView // ignore: cast_nullable_to_non_nullable
              as ProductionView,
      selectedItemIds: null == selectedItemIds
          ? _value.selectedItemIds
          : selectedItemIds // ignore: cast_nullable_to_non_nullable
              as Set<String>,
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      statusFilter: freezed == statusFilter
          ? _value.statusFilter
          : statusFilter // ignore: cast_nullable_to_non_nullable
              as ProductionTaskStatus?,
      dateFromFilter: freezed == dateFromFilter
          ? _value.dateFromFilter
          : dateFromFilter // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      dateToFilter: freezed == dateToFilter
          ? _value.dateToFilter
          : dateToFilter // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isLoadingItems: null == isLoadingItems
          ? _value.isLoadingItems
          : isLoadingItems // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingTasks: null == isLoadingTasks
          ? _value.isLoadingTasks
          : isLoadingTasks // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingTaskDetails: null == isLoadingTaskDetails
          ? _value.isLoadingTaskDetails
          : isLoadingTaskDetails // ignore: cast_nullable_to_non_nullable
              as bool,
      isCreatingTask: null == isCreatingTask
          ? _value.isCreatingTask
          : isCreatingTask // ignore: cast_nullable_to_non_nullable
              as bool,
      isCancellingTask: null == isCancellingTask
          ? _value.isCancellingTask
          : isCancellingTask // ignore: cast_nullable_to_non_nullable
              as bool,
      isUpdatingStatus: null == isUpdatingStatus
          ? _value.isUpdatingStatus
          : isUpdatingStatus // ignore: cast_nullable_to_non_nullable
              as bool,
      itemsError: freezed == itemsError
          ? _value.itemsError
          : itemsError // ignore: cast_nullable_to_non_nullable
              as String?,
      tasksError: freezed == tasksError
          ? _value.tasksError
          : tasksError // ignore: cast_nullable_to_non_nullable
              as String?,
      taskDetailsError: freezed == taskDetailsError
          ? _value.taskDetailsError
          : taskDetailsError // ignore: cast_nullable_to_non_nullable
              as String?,
      createTaskError: freezed == createTaskError
          ? _value.createTaskError
          : createTaskError // ignore: cast_nullable_to_non_nullable
              as String?,
      cancelTaskError: freezed == cancelTaskError
          ? _value.cancelTaskError
          : cancelTaskError // ignore: cast_nullable_to_non_nullable
              as String?,
      updateStatusError: freezed == updateStatusError
          ? _value.updateStatusError
          : updateStatusError // ignore: cast_nullable_to_non_nullable
              as String?,
      successMessage: freezed == successMessage
          ? _value.successMessage
          : successMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of ProductionState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProductionTaskModelCopyWith<$Res>? get selectedTaskDetails {
    if (_value.selectedTaskDetails == null) {
      return null;
    }

    return $ProductionTaskModelCopyWith<$Res>(_value.selectedTaskDetails!,
        (value) {
      return _then(_value.copyWith(selectedTaskDetails: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ProductionStateImplCopyWith<$Res>
    implements $ProductionStateCopyWith<$Res> {
  factory _$$ProductionStateImplCopyWith(_$ProductionStateImpl value,
          $Res Function(_$ProductionStateImpl) then) =
      __$$ProductionStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<ProductionItemModel> items,
      List<ProductionTaskModel> tasks,
      ProductionTaskModel? selectedTaskDetails,
      ProductionView currentView,
      Set<String> selectedItemIds,
      String? projectId,
      ProductionTaskStatus? statusFilter,
      DateTime? dateFromFilter,
      DateTime? dateToFilter,
      bool isLoadingItems,
      bool isLoadingTasks,
      bool isLoadingTaskDetails,
      bool isCreatingTask,
      bool isCancellingTask,
      bool isUpdatingStatus,
      String? itemsError,
      String? tasksError,
      String? taskDetailsError,
      String? createTaskError,
      String? cancelTaskError,
      String? updateStatusError,
      String? successMessage});

  @override
  $ProductionTaskModelCopyWith<$Res>? get selectedTaskDetails;
}

/// @nodoc
class __$$ProductionStateImplCopyWithImpl<$Res>
    extends _$ProductionStateCopyWithImpl<$Res, _$ProductionStateImpl>
    implements _$$ProductionStateImplCopyWith<$Res> {
  __$$ProductionStateImplCopyWithImpl(
      _$ProductionStateImpl _value, $Res Function(_$ProductionStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductionState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = null,
    Object? tasks = null,
    Object? selectedTaskDetails = freezed,
    Object? currentView = null,
    Object? selectedItemIds = null,
    Object? projectId = freezed,
    Object? statusFilter = freezed,
    Object? dateFromFilter = freezed,
    Object? dateToFilter = freezed,
    Object? isLoadingItems = null,
    Object? isLoadingTasks = null,
    Object? isLoadingTaskDetails = null,
    Object? isCreatingTask = null,
    Object? isCancellingTask = null,
    Object? isUpdatingStatus = null,
    Object? itemsError = freezed,
    Object? tasksError = freezed,
    Object? taskDetailsError = freezed,
    Object? createTaskError = freezed,
    Object? cancelTaskError = freezed,
    Object? updateStatusError = freezed,
    Object? successMessage = freezed,
  }) {
    return _then(_$ProductionStateImpl(
      items: null == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ProductionItemModel>,
      tasks: null == tasks
          ? _value._tasks
          : tasks // ignore: cast_nullable_to_non_nullable
              as List<ProductionTaskModel>,
      selectedTaskDetails: freezed == selectedTaskDetails
          ? _value.selectedTaskDetails
          : selectedTaskDetails // ignore: cast_nullable_to_non_nullable
              as ProductionTaskModel?,
      currentView: null == currentView
          ? _value.currentView
          : currentView // ignore: cast_nullable_to_non_nullable
              as ProductionView,
      selectedItemIds: null == selectedItemIds
          ? _value._selectedItemIds
          : selectedItemIds // ignore: cast_nullable_to_non_nullable
              as Set<String>,
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      statusFilter: freezed == statusFilter
          ? _value.statusFilter
          : statusFilter // ignore: cast_nullable_to_non_nullable
              as ProductionTaskStatus?,
      dateFromFilter: freezed == dateFromFilter
          ? _value.dateFromFilter
          : dateFromFilter // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      dateToFilter: freezed == dateToFilter
          ? _value.dateToFilter
          : dateToFilter // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isLoadingItems: null == isLoadingItems
          ? _value.isLoadingItems
          : isLoadingItems // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingTasks: null == isLoadingTasks
          ? _value.isLoadingTasks
          : isLoadingTasks // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingTaskDetails: null == isLoadingTaskDetails
          ? _value.isLoadingTaskDetails
          : isLoadingTaskDetails // ignore: cast_nullable_to_non_nullable
              as bool,
      isCreatingTask: null == isCreatingTask
          ? _value.isCreatingTask
          : isCreatingTask // ignore: cast_nullable_to_non_nullable
              as bool,
      isCancellingTask: null == isCancellingTask
          ? _value.isCancellingTask
          : isCancellingTask // ignore: cast_nullable_to_non_nullable
              as bool,
      isUpdatingStatus: null == isUpdatingStatus
          ? _value.isUpdatingStatus
          : isUpdatingStatus // ignore: cast_nullable_to_non_nullable
              as bool,
      itemsError: freezed == itemsError
          ? _value.itemsError
          : itemsError // ignore: cast_nullable_to_non_nullable
              as String?,
      tasksError: freezed == tasksError
          ? _value.tasksError
          : tasksError // ignore: cast_nullable_to_non_nullable
              as String?,
      taskDetailsError: freezed == taskDetailsError
          ? _value.taskDetailsError
          : taskDetailsError // ignore: cast_nullable_to_non_nullable
              as String?,
      createTaskError: freezed == createTaskError
          ? _value.createTaskError
          : createTaskError // ignore: cast_nullable_to_non_nullable
              as String?,
      cancelTaskError: freezed == cancelTaskError
          ? _value.cancelTaskError
          : cancelTaskError // ignore: cast_nullable_to_non_nullable
              as String?,
      updateStatusError: freezed == updateStatusError
          ? _value.updateStatusError
          : updateStatusError // ignore: cast_nullable_to_non_nullable
              as String?,
      successMessage: freezed == successMessage
          ? _value.successMessage
          : successMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$ProductionStateImpl extends _ProductionState {
  const _$ProductionStateImpl(
      {final List<ProductionItemModel> items = const [],
      final List<ProductionTaskModel> tasks = const [],
      this.selectedTaskDetails,
      this.currentView = ProductionView.items,
      final Set<String> selectedItemIds = const {},
      this.projectId,
      this.statusFilter,
      this.dateFromFilter,
      this.dateToFilter,
      this.isLoadingItems = false,
      this.isLoadingTasks = false,
      this.isLoadingTaskDetails = false,
      this.isCreatingTask = false,
      this.isCancellingTask = false,
      this.isUpdatingStatus = false,
      this.itemsError,
      this.tasksError,
      this.taskDetailsError,
      this.createTaskError,
      this.cancelTaskError,
      this.updateStatusError,
      this.successMessage})
      : _items = items,
        _tasks = tasks,
        _selectedItemIds = selectedItemIds,
        super._();

// Data
  final List<ProductionItemModel> _items;
// Data
  @override
  @JsonKey()
  List<ProductionItemModel> get items {
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_items);
  }

  final List<ProductionTaskModel> _tasks;
  @override
  @JsonKey()
  List<ProductionTaskModel> get tasks {
    if (_tasks is EqualUnmodifiableListView) return _tasks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tasks);
  }

  @override
  final ProductionTaskModel? selectedTaskDetails;
// UI State
  @override
  @JsonKey()
  final ProductionView currentView;
  final Set<String> _selectedItemIds;
  @override
  @JsonKey()
  Set<String> get selectedItemIds {
    if (_selectedItemIds is EqualUnmodifiableSetView) return _selectedItemIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_selectedItemIds);
  }

// Filters
  @override
  final String? projectId;
  @override
  final ProductionTaskStatus? statusFilter;
  @override
  final DateTime? dateFromFilter;
  @override
  final DateTime? dateToFilter;
// Loading States
  @override
  @JsonKey()
  final bool isLoadingItems;
  @override
  @JsonKey()
  final bool isLoadingTasks;
  @override
  @JsonKey()
  final bool isLoadingTaskDetails;
  @override
  @JsonKey()
  final bool isCreatingTask;
  @override
  @JsonKey()
  final bool isCancellingTask;
  @override
  @JsonKey()
  final bool isUpdatingStatus;
// Error States
  @override
  final String? itemsError;
  @override
  final String? tasksError;
  @override
  final String? taskDetailsError;
  @override
  final String? createTaskError;
  @override
  final String? cancelTaskError;
  @override
  final String? updateStatusError;
// Success Messages
  @override
  final String? successMessage;

  @override
  String toString() {
    return 'ProductionState(items: $items, tasks: $tasks, selectedTaskDetails: $selectedTaskDetails, currentView: $currentView, selectedItemIds: $selectedItemIds, projectId: $projectId, statusFilter: $statusFilter, dateFromFilter: $dateFromFilter, dateToFilter: $dateToFilter, isLoadingItems: $isLoadingItems, isLoadingTasks: $isLoadingTasks, isLoadingTaskDetails: $isLoadingTaskDetails, isCreatingTask: $isCreatingTask, isCancellingTask: $isCancellingTask, isUpdatingStatus: $isUpdatingStatus, itemsError: $itemsError, tasksError: $tasksError, taskDetailsError: $taskDetailsError, createTaskError: $createTaskError, cancelTaskError: $cancelTaskError, updateStatusError: $updateStatusError, successMessage: $successMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductionStateImpl &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            const DeepCollectionEquality().equals(other._tasks, _tasks) &&
            (identical(other.selectedTaskDetails, selectedTaskDetails) ||
                other.selectedTaskDetails == selectedTaskDetails) &&
            (identical(other.currentView, currentView) ||
                other.currentView == currentView) &&
            const DeepCollectionEquality()
                .equals(other._selectedItemIds, _selectedItemIds) &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            (identical(other.statusFilter, statusFilter) ||
                other.statusFilter == statusFilter) &&
            (identical(other.dateFromFilter, dateFromFilter) ||
                other.dateFromFilter == dateFromFilter) &&
            (identical(other.dateToFilter, dateToFilter) ||
                other.dateToFilter == dateToFilter) &&
            (identical(other.isLoadingItems, isLoadingItems) ||
                other.isLoadingItems == isLoadingItems) &&
            (identical(other.isLoadingTasks, isLoadingTasks) ||
                other.isLoadingTasks == isLoadingTasks) &&
            (identical(other.isLoadingTaskDetails, isLoadingTaskDetails) ||
                other.isLoadingTaskDetails == isLoadingTaskDetails) &&
            (identical(other.isCreatingTask, isCreatingTask) ||
                other.isCreatingTask == isCreatingTask) &&
            (identical(other.isCancellingTask, isCancellingTask) ||
                other.isCancellingTask == isCancellingTask) &&
            (identical(other.isUpdatingStatus, isUpdatingStatus) ||
                other.isUpdatingStatus == isUpdatingStatus) &&
            (identical(other.itemsError, itemsError) ||
                other.itemsError == itemsError) &&
            (identical(other.tasksError, tasksError) ||
                other.tasksError == tasksError) &&
            (identical(other.taskDetailsError, taskDetailsError) ||
                other.taskDetailsError == taskDetailsError) &&
            (identical(other.createTaskError, createTaskError) ||
                other.createTaskError == createTaskError) &&
            (identical(other.cancelTaskError, cancelTaskError) ||
                other.cancelTaskError == cancelTaskError) &&
            (identical(other.updateStatusError, updateStatusError) ||
                other.updateStatusError == updateStatusError) &&
            (identical(other.successMessage, successMessage) ||
                other.successMessage == successMessage));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        const DeepCollectionEquality().hash(_items),
        const DeepCollectionEquality().hash(_tasks),
        selectedTaskDetails,
        currentView,
        const DeepCollectionEquality().hash(_selectedItemIds),
        projectId,
        statusFilter,
        dateFromFilter,
        dateToFilter,
        isLoadingItems,
        isLoadingTasks,
        isLoadingTaskDetails,
        isCreatingTask,
        isCancellingTask,
        isUpdatingStatus,
        itemsError,
        tasksError,
        taskDetailsError,
        createTaskError,
        cancelTaskError,
        updateStatusError,
        successMessage
      ]);

  /// Create a copy of ProductionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductionStateImplCopyWith<_$ProductionStateImpl> get copyWith =>
      __$$ProductionStateImplCopyWithImpl<_$ProductionStateImpl>(
          this, _$identity);
}

abstract class _ProductionState extends ProductionState {
  const factory _ProductionState(
      {final List<ProductionItemModel> items,
      final List<ProductionTaskModel> tasks,
      final ProductionTaskModel? selectedTaskDetails,
      final ProductionView currentView,
      final Set<String> selectedItemIds,
      final String? projectId,
      final ProductionTaskStatus? statusFilter,
      final DateTime? dateFromFilter,
      final DateTime? dateToFilter,
      final bool isLoadingItems,
      final bool isLoadingTasks,
      final bool isLoadingTaskDetails,
      final bool isCreatingTask,
      final bool isCancellingTask,
      final bool isUpdatingStatus,
      final String? itemsError,
      final String? tasksError,
      final String? taskDetailsError,
      final String? createTaskError,
      final String? cancelTaskError,
      final String? updateStatusError,
      final String? successMessage}) = _$ProductionStateImpl;
  const _ProductionState._() : super._();

// Data
  @override
  List<ProductionItemModel> get items;
  @override
  List<ProductionTaskModel> get tasks;
  @override
  ProductionTaskModel? get selectedTaskDetails; // UI State
  @override
  ProductionView get currentView;
  @override
  Set<String> get selectedItemIds; // Filters
  @override
  String? get projectId;
  @override
  ProductionTaskStatus? get statusFilter;
  @override
  DateTime? get dateFromFilter;
  @override
  DateTime? get dateToFilter; // Loading States
  @override
  bool get isLoadingItems;
  @override
  bool get isLoadingTasks;
  @override
  bool get isLoadingTaskDetails;
  @override
  bool get isCreatingTask;
  @override
  bool get isCancellingTask;
  @override
  bool get isUpdatingStatus; // Error States
  @override
  String? get itemsError;
  @override
  String? get tasksError;
  @override
  String? get taskDetailsError;
  @override
  String? get createTaskError;
  @override
  String? get cancelTaskError;
  @override
  String? get updateStatusError; // Success Messages
  @override
  String? get successMessage;

  /// Create a copy of ProductionState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductionStateImplCopyWith<_$ProductionStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
