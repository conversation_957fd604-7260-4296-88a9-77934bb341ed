import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/features/_initial/nomenclatures/data/models/material.dart';
import 'package:sphere/features/_initial/storage/data/models/material.dart';
import 'package:sphere/features/product/data/models/product.dart';

part 'index.freezed.dart';
part 'index.g.dart';

// Request models
@freezed
class ProductionItemsRequest with _$ProductionItemsRequest {
  @JsonSerializable(includeIfNull: false)
  const factory ProductionItemsRequest({
    required String projectId,
  }) = _ProductionItemsRequest;

  factory ProductionItemsRequest.fromJson(Map<String, dynamic> json) =>
      _$ProductionItemsRequestFromJson(json);
}

@freezed
class ProductionTaskCancelRequest with _$ProductionTaskCancelRequest {
  @JsonSerializable(includeIfNull: false)
  const factory ProductionTaskCancelRequest({
    required String taskId,
    required String reason,
  }) = _ProductionTaskCancelRequest;

  factory ProductionTaskCancelRequest.fromJson(Map<String, dynamic> json) =>
      _$ProductionTaskCancelRequestFromJson(json);
}

@freezed
class ProductionTaskCreateRequest with _$ProductionTaskCreateRequest {
  @JsonSerializable(includeIfNull: false)
  const factory ProductionTaskCreateRequest({
    String? projectId,
    String? productId,
    double? quantityRequired,
  }) = _ProductionTaskCreateRequest;

  factory ProductionTaskCreateRequest.fromJson(Map<String, dynamic> json) =>
      _$ProductionTaskCreateRequestFromJson(json);
}

@freezed
class ProductionTaskDetailsRequest with _$ProductionTaskDetailsRequest {
  @JsonSerializable(includeIfNull: false)
  const factory ProductionTaskDetailsRequest({
    required String taskId,
  }) = _ProductionTaskDetailsRequest;

  factory ProductionTaskDetailsRequest.fromJson(Map<String, dynamic> json) =>
      _$ProductionTaskDetailsRequestFromJson(json);
}

@freezed
class ProductionTaskUpdateStatusRequest
    with _$ProductionTaskUpdateStatusRequest {
  @JsonSerializable(includeIfNull: false)
  const factory ProductionTaskUpdateStatusRequest({
    required String taskId,
    required ProductionTaskStatus status,
    String? comment,
  }) = _ProductionTaskUpdateStatusRequest;

  factory ProductionTaskUpdateStatusRequest.fromJson(
          Map<String, dynamic> json) =>
      _$ProductionTaskUpdateStatusRequestFromJson(json);
}

@freezed
class ProductionTasksRequest with _$ProductionTasksRequest {
  @JsonSerializable(includeIfNull: false)
  const factory ProductionTasksRequest({
    String? projectId,
    String? productId,
    ProductionTaskStatus? status,
    DateTime? dateFrom,
    DateTime? dateTo,
  }) = _ProductionTasksRequest;

  factory ProductionTasksRequest.fromJson(Map<String, dynamic> json) =>
      _$ProductionTasksRequestFromJson(json);
}

// Response models
@freezed
class ProductionItemsResponse with _$ProductionItemsResponse {
  @JsonSerializable(includeIfNull: false)
  const factory ProductionItemsResponse({
    List<ProductionItemModel>? items,
    int? totalItems,
  }) = _ProductionItemsResponse;

  factory ProductionItemsResponse.fromJson(Map<String, dynamic> json) =>
      _$ProductionItemsResponseFromJson(json);
}

@freezed
class ProductionTaskCancelResponse with _$ProductionTaskCancelResponse {
  @JsonSerializable(includeIfNull: false)
  const factory ProductionTaskCancelResponse({
    bool? success,
    String? message,
  }) = _ProductionTaskCancelResponse;

  factory ProductionTaskCancelResponse.fromJson(Map<String, dynamic> json) =>
      _$ProductionTaskCancelResponseFromJson(json);
}

@freezed
class ProductionTasksResponse with _$ProductionTasksResponse {
  @JsonSerializable(includeIfNull: false)
  const factory ProductionTasksResponse({
    List<ProductionTaskModel>? items,
    int? totalItems,
  }) = _ProductionTasksResponse;

  factory ProductionTasksResponse.fromJson(Map<String, dynamic> json) =>
      _$ProductionTasksResponseFromJson(json);
}

// Main models
@freezed
class ProductionItemModel with _$ProductionItemModel {
  @JsonSerializable(includeIfNull: false)
  const factory ProductionItemModel({
    String? productId,
    ProductModel? product,
    double? requiredQuantity,
    double? readyQuantity,
    ProductionStatus? status,
    double? filledPercentage,
    List<ProductionMaterialModel>? materials,
    ProductionTaskModel? productionTask,
  }) = _ProductionItemModel;

  factory ProductionItemModel.fromJson(Map<String, dynamic> json) =>
      _$ProductionItemModelFromJson(json);
}

@freezed
class ProductionMaterialModel with _$ProductionMaterialModel {
  @JsonSerializable(includeIfNull: false)
  const factory ProductionMaterialModel({
    String? materialId,
    NomenclatureModel? material,
    String? materialRequirements,
    double? required,
    double? available,
    double? filledPercentage,
    MaterialModel? storageItem,
  }) = _ProductionMaterialModel;

  factory ProductionMaterialModel.fromJson(Map<String, dynamic> json) =>
      _$ProductionMaterialModelFromJson(json);
}

@freezed
class ProductionTaskModel with _$ProductionTaskModel {
  @JsonSerializable(includeIfNull: false)
  const factory ProductionTaskModel({
    @JsonKey(name: '_id') String? id,
    String? projectId,
    String? productId,
    ProductionTaskStatus? status,
    double? quantityRequired,
    List<ProductionTaskMaterialModel>? materials,
    List<ProductionTaskStatusHistoryModel>? statusHistory,
    String? requestedBy,
    String? issuedBy,
    String? assembledBy,
    String? inspectedBy,
    bool? cancelled,
    DateTime? cancelledAt,
    String? cancelledBy,
    String? cancelReason,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _ProductionTaskModel;

  factory ProductionTaskModel.fromJson(Map<String, dynamic> json) =>
      _$ProductionTaskModelFromJson(json);
}

@freezed
class ProductionTaskMaterialModel with _$ProductionTaskMaterialModel {
  @JsonSerializable(includeIfNull: false)
  const factory ProductionTaskMaterialModel({
    String? storageItemId,
    MaterialModel? storageItem,
    double? requiredQuantity,
    double? issuedQuantity,
    String? materialName,
    String? materialUnit,
  }) = _ProductionTaskMaterialModel;

  factory ProductionTaskMaterialModel.fromJson(Map<String, dynamic> json) =>
      _$ProductionTaskMaterialModelFromJson(json);
}

@freezed
class ProductionTaskStatusHistoryModel with _$ProductionTaskStatusHistoryModel {
  @JsonSerializable(includeIfNull: false)
  const factory ProductionTaskStatusHistoryModel({
    ProductionTaskStatus? status,
    DateTime? date,
    String? userId,
    String? comment,
  }) = _ProductionTaskStatusHistoryModel;

  factory ProductionTaskStatusHistoryModel.fromJson(
          Map<String, dynamic> json) =>
      _$ProductionTaskStatusHistoryModelFromJson(json);
}

// Enums
enum ProductionStatus {
  @JsonValue('ready')
  ready,
  @JsonValue('partial')
  partial,
  @JsonValue('not_ready')
  notReady,
  @JsonValue('pending_sub_assemblies')
  pendingSubAssemblies;

  String getName() {
    switch (this) {
      case ProductionStatus.ready:
        return 'Готов к производству';
      case ProductionStatus.partial:
        return 'Частично готов';
      case ProductionStatus.notReady:
        return 'Не готов';
      case ProductionStatus.pendingSubAssemblies:
        return 'Ожидание подсборок';
    }
  }
}

enum ProductionTaskStatus {
  @JsonValue('materials_requested')
  materialsRequested,
  @JsonValue('materials_issued')
  materialsIssued,
  @JsonValue('assembly_in_progress')
  assemblyInProgress,
  @JsonValue('ready_for_inspection')
  readyForInspection,
  @JsonValue('qc_inspection')
  qcInspection,
  @JsonValue('qc_approved')
  qcApproved;

  String getName() {
    switch (this) {
      case ProductionTaskStatus.materialsRequested:
        return 'Материалы запрошены';
      case ProductionTaskStatus.materialsIssued:
        return 'Материалы выданы';
      case ProductionTaskStatus.assemblyInProgress:
        return 'Сборка в процессе';
      case ProductionTaskStatus.readyForInspection:
        return 'Готово для проверки';
      case ProductionTaskStatus.qcInspection:
        return 'Контроль качества';
      case ProductionTaskStatus.qcApproved:
        return 'Принято ОТК';
    }
  }
}
