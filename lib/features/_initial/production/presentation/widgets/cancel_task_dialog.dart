import 'package:flutter/material.dart';
import 'package:sphere/features/_initial/production/data/models/index.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/ghost_button.dart';

class CancelTaskDialog extends StatefulWidget {
  final ProductionTaskModel task;
  final Function(ProductionTaskCancelRequest request) onTaskCancelled;

  const CancelTaskDialog({
    super.key,
    required this.task,
    required this.onTaskCancelled,
  });

  @override
  State<CancelTaskDialog> createState() => _CancelTaskDialogState();
}

class _CancelTaskDialogState extends State<CancelTaskDialog> {
  final _formKey = GlobalKey<FormState>();
  final _reasonController = TextEditingController();

  @override
  void dispose() {
    _reasonController.dispose();
    super.dispose();
  }

  void _cancelTask() {
    if (_formKey.currentState?.validate() ?? false) {
      final request = ProductionTaskCancelRequest(
        taskId: widget.task.id,
        reason: _reasonController.text.trim(),
      );

      widget.onTaskCancelled(request);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 500,
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.warning,
                    color: AppColors.lightError,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Отменить задание',
                    style: Fonts.headlineSmall.copyWith(
                      color: AppColors.lightError,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.lightError.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: AppColors.lightError.withValues(alpha: 0.3),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Внимание!',
                      style: Fonts.labelMedium.copyWith(
                        color: AppColors.lightError,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Вы собираетесь отменить производственное задание. Это действие нельзя будет отменить.',
                      style: Fonts.bodySmall,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Theme.of(context).dividerColor,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Информация о задании',
                      style: Fonts.labelMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'ID: ${widget.task.id?.substring(0, 8) ?? 'N/A'}',
                      style: Fonts.bodySmall.copyWith(
                        color: Theme.of(context).textTheme.bodySmall?.color,
                      ),
                    ),
                    Text(
                      'Продукт: ${widget.task.productId ?? 'N/A'}',
                      style: Fonts.bodySmall.copyWith(
                        color: Theme.of(context).textTheme.bodySmall?.color,
                      ),
                    ),
                    Text(
                      'Статус: ${widget.task.status?.getName() ?? 'Неизвестно'}',
                      style: Fonts.bodySmall.copyWith(
                        color: Theme.of(context).textTheme.bodySmall?.color,
                      ),
                    ),
                    Text(
                      'Количество: ${widget.task.quantityRequired?.toString() ?? 'N/A'}',
                      style: Fonts.bodySmall.copyWith(
                        color: Theme.of(context).textTheme.bodySmall?.color,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _reasonController,
                decoration: const InputDecoration(
                  labelText: 'Причина отмены *',
                  hintText: 'Укажите причину отмены задания',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Укажите причину отмены';
                  }
                  if (value.trim().length < 10) {
                    return 'Причина должна содержать минимум 10 символов';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  GhostButton(
                    onTap: () => Navigator.of(context).pop(),
                    child: const Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      child: Text('Отмена'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton(
                    onPressed: _cancelTask,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.lightError,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Отменить задание'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
