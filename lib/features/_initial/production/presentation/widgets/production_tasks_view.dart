import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:sphere/features/_initial/production/data/models/index.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';
import 'package:sphere/shared/widgets/interactive/ghost_button.dart';

class ProductionTasksView extends StatefulWidget {
  final List<ProductionTaskModel> tasks;
  final bool isLoading;
  final String? error;
  final Function(String taskId) onTaskTap;
  final Function(ProductionTaskModel task) onUpdateStatus;
  final Function(ProductionTaskModel task) onCancelTask;
  final Function(
          ProductionTaskStatus? status, DateTime? dateFrom, DateTime? dateTo)
      onFilterChanged;

  const ProductionTasksView({
    super.key,
    required this.tasks,
    required this.isLoading,
    this.error,
    required this.onTaskTap,
    required this.onUpdateStatus,
    required this.onCancelTask,
    required this.onFilterChanged,
  });

  @override
  State<ProductionTasksView> createState() => _ProductionTasksViewState();
}

class _ProductionTasksViewState extends State<ProductionTasksView> {
  ProductionTaskStatus? _selectedStatus;
  DateTime? _dateFrom;
  DateTime? _dateTo;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildFilters(),
        const SizedBox(height: 16),
        Expanded(child: _buildTasksList()),
      ],
    );
  }

  Widget _buildFilters() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Theme.of(context).dividerColor),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: DropdownButtonFormField<ProductionTaskStatus?>(
              value: _selectedStatus,
              decoration: const InputDecoration(
                labelText: 'Статус',
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: [
                const DropdownMenuItem<ProductionTaskStatus?>(
                  value: null,
                  child: Text('Все статусы'),
                ),
                ...ProductionTaskStatus.values.map(
                  (status) => DropdownMenuItem<ProductionTaskStatus?>(
                    value: status,
                    child: Text(status.getName()),
                  ),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedStatus = value;
                });
                widget.onFilterChanged(value, _dateFrom, _dateTo);
              },
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () => _selectDate(context, true),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 12),
                      decoration: BoxDecoration(
                        border:
                            Border.all(color: Theme.of(context).dividerColor),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        _dateFrom != null
                            ? DateFormat('dd.MM.yyyy').format(_dateFrom!)
                            : 'Дата от',
                        style: Fonts.bodyMedium,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: GestureDetector(
                    onTap: () => _selectDate(context, false),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 12),
                      decoration: BoxDecoration(
                        border:
                            Border.all(color: Theme.of(context).dividerColor),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        _dateTo != null
                            ? DateFormat('dd.MM.yyyy').format(_dateTo!)
                            : 'Дата до',
                        style: Fonts.bodyMedium,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          GhostButton(
            onTap: _clearFilters,
            child: const Padding(
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: Text('Сбросить'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTasksList() {
    if (widget.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (widget.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Ошибка загрузки',
              style: Fonts.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              widget.error!,
              style: Fonts.bodyMedium.copyWith(color: AppColors.lightError),
            ),
          ],
        ),
      );
    }

    if (widget.tasks.isEmpty) {
      return const Center(
        child: Text(
          'Нет производственных заданий',
          style: TextStyle(fontSize: 16),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: DataTable2(
        minWidth: 1400,
        dividerThickness: 1.0,
        showCheckboxColumn: false,
        showBottomBorder: true,
        columnSpacing: 12,
        columns: const [
          DataColumn2(
            label: Text('ID'),
            size: ColumnSize.S,
          ),
          DataColumn2(
            label: Text('Продукт'),
            size: ColumnSize.L,
          ),
          DataColumn2(
            label: Text('Статус'),
            size: ColumnSize.M,
          ),
          DataColumn2(
            label: Text('Количество'),
            size: ColumnSize.S,
          ),
          DataColumn2(
            label: Text('Создано'),
            size: ColumnSize.M,
          ),
          DataColumn2(
            label: Text('Обновлено'),
            size: ColumnSize.M,
          ),
          DataColumn2(
            label: Text('Действия'),
            size: ColumnSize.L,
          ),
        ],
        rows: widget.tasks.map((task) => _buildTaskRow(context, task)).toList(),
      ),
    );
  }

  DataRow2 _buildTaskRow(BuildContext context, ProductionTaskModel task) {
    return DataRow2(
      onTap: () => widget.onTaskTap(task.id ?? ''),
      cells: [
        DataCell(
          Text(
            task.id?.substring(0, 8) ?? 'N/A',
            style: Fonts.bodySmall.copyWith(
              fontFamily: 'monospace',
            ),
          ),
        ),
        DataCell(
          Text(
            task.productId ?? 'Неизвестно',
            style: Fonts.labelMedium,
          ),
        ),
        DataCell(
          _buildStatusChip(task.status),
        ),
        DataCell(
          Text(
            task.quantityRequired?.toStringAsFixed(2) ?? '0',
            style: Fonts.bodyMedium,
          ),
        ),
        DataCell(
          Text(
            task.createdAt != null
                ? DateFormat('dd.MM.yyyy HH:mm').format(task.createdAt!)
                : '—',
            style: Fonts.bodySmall,
          ),
        ),
        DataCell(
          Text(
            task.updatedAt != null
                ? DateFormat('dd.MM.yyyy HH:mm').format(task.updatedAt!)
                : '—',
            style: Fonts.bodySmall,
          ),
        ),
        DataCell(
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CustomElevatedButton(
                onPressed: () => widget.onTaskTap(task.id ?? ''),
                text: 'Детали',
                style: Fonts.labelSmall,
              ),
              const SizedBox(width: 8),
              CustomElevatedButton(
                onPressed: () => widget.onUpdateStatus(task),
                text: 'Статус',
                style: Fonts.labelSmall,
              ),
              const SizedBox(width: 8),
              if (task.cancelled != true) ...[
                GhostButton(
                  onTap: () => widget.onCancelTask(task),
                  child: Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    child: Text(
                      'Отменить',
                      style: Fonts.labelSmall
                          .copyWith(color: AppColors.lightError),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatusChip(ProductionTaskStatus? status) {
    if (status == null) return const Text('—');

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getTaskStatusColor(status),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        status.getName(),
        style: Fonts.labelSmall.copyWith(color: Colors.white),
      ),
    );
  }

  Color _getTaskStatusColor(ProductionTaskStatus status) {
    switch (status) {
      case ProductionTaskStatus.materialsRequested:
        return AppColors.lightWarning;
      case ProductionTaskStatus.materialsIssued:
        return AppColors.lightPurple;
      case ProductionTaskStatus.assemblyInProgress:
        return AppColors.lightPrimary;
      case ProductionTaskStatus.readyForInspection:
        return AppColors.lightPurple;
      case ProductionTaskStatus.qcInspection:
        return AppColors.lightWarning;
      case ProductionTaskStatus.qcApproved:
        return AppColors.lightSuccess;
    }
  }

  Future<void> _selectDate(BuildContext context, bool isFromDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          isFromDate ? _dateFrom ?? DateTime.now() : _dateTo ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (picked != null) {
      setState(() {
        if (isFromDate) {
          _dateFrom = picked;
        } else {
          _dateTo = picked;
        }
      });
      widget.onFilterChanged(_selectedStatus, _dateFrom, _dateTo);
    }
  }

  void _clearFilters() {
    setState(() {
      _selectedStatus = null;
      _dateFrom = null;
      _dateTo = null;
    });
    widget.onFilterChanged(null, null, null);
  }
}
