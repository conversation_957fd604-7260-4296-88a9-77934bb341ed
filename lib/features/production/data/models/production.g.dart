// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'production.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ProductionModelImpl _$$ProductionModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductionModelImpl(
      productId: json['productId'] as String?,
      product: json['product'] == null
          ? null
          : ProductModel.fromJson(json['product'] as Map<String, dynamic>),
      requiredQuantity: (json['requiredQuantity'] as num?)?.toDouble(),
      readyQuantity: (json['readyQuantity'] as num?)?.toDouble(),
      status: $enumDecodeNullable(_$ProductionStatusEnumMap, json['status']),
      filledPercentage: (json['filledPercentage'] as num?)?.toDouble(),
      materials: (json['materials'] as List<dynamic>?)
          ?.map((e) => MaterialModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      productionTask: json['productionTask'] == null
          ? null
          : ProductionTaskModel.fromJson(
              json['productionTask'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$ProductionModelImplToJson(
        _$ProductionModelImpl instance) =>
    <String, dynamic>{
      if (instance.productId case final value?) 'productId': value,
      if (instance.product case final value?) 'product': value,
      if (instance.requiredQuantity case final value?)
        'requiredQuantity': value,
      if (instance.readyQuantity case final value?) 'readyQuantity': value,
      if (_$ProductionStatusEnumMap[instance.status] case final value?)
        'status': value,
      if (instance.filledPercentage case final value?)
        'filledPercentage': value,
      if (instance.materials case final value?) 'materials': value,
      if (instance.productionTask case final value?) 'productionTask': value,
    };

const _$ProductionStatusEnumMap = {
  ProductionStatus.ready: 'ready',
  ProductionStatus.partial: 'partial',
  ProductionStatus.notReady: 'not_ready',
  ProductionStatus.pendingSubAssemblies: 'pending_sub_assemblies',
};

_$ProductionTaskModelImpl _$$ProductionTaskModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductionTaskModelImpl(
      id: json['_id'] as String?,
      projcectId: json['projcectId'] as String?,
      productId: json['productId'] as String?,
      status:
          $enumDecodeNullable(_$ProductionTaskStatusEnumMap, json['status']),
      quantityRequired: (json['quantityRequired'] as num?)?.toDouble(),
      materials: (json['materials'] as List<dynamic>?)
          ?.map((e) =>
              ProductionMaterialModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      statusHistory: (json['statusHistory'] as List<dynamic>?)
          ?.map((e) =>
              ProductionStatusHistoryModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$$ProductionTaskModelImplToJson(
        _$ProductionTaskModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) '_id': value,
      if (instance.projcectId case final value?) 'projcectId': value,
      if (instance.productId case final value?) 'productId': value,
      if (_$ProductionTaskStatusEnumMap[instance.status] case final value?)
        'status': value,
      if (instance.quantityRequired case final value?)
        'quantityRequired': value,
      if (instance.materials case final value?) 'materials': value,
      if (instance.statusHistory case final value?) 'statusHistory': value,
      if (instance.updatedAt?.toIso8601String() case final value?)
        'updatedAt': value,
      if (instance.createdAt?.toIso8601String() case final value?)
        'createdAt': value,
    };

const _$ProductionTaskStatusEnumMap = {
  ProductionTaskStatus.materialsRequested: 'materials_requested',
  ProductionTaskStatus.materialsIssued: 'materials_issued',
  ProductionTaskStatus.assemblyInProgress: 'assembly_in_progress',
  ProductionTaskStatus.readyForInspection: 'ready_for_inspection',
  ProductionTaskStatus.qcInspection: 'qc_inspection',
  ProductionTaskStatus.qcApproved: 'qc_approved',
};

_$ProductionStatusHistoryModelImpl _$$ProductionStatusHistoryModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductionStatusHistoryModelImpl(
      status:
          $enumDecodeNullable(_$ProductionTaskStatusEnumMap, json['status']),
      date:
          json['date'] == null ? null : DateTime.parse(json['date'] as String),
      userId: json['userId'] as String?,
      user: json['user'] == null
          ? null
          : UserModel.fromJson(json['user'] as Map<String, dynamic>),
      comment: json['comment'] as String?,
    );

Map<String, dynamic> _$$ProductionStatusHistoryModelImplToJson(
        _$ProductionStatusHistoryModelImpl instance) =>
    <String, dynamic>{
      if (_$ProductionTaskStatusEnumMap[instance.status] case final value?)
        'status': value,
      if (instance.date?.toIso8601String() case final value?) 'date': value,
      if (instance.userId case final value?) 'userId': value,
      if (instance.user case final value?) 'user': value,
      if (instance.comment case final value?) 'comment': value,
    };

_$ProductionMaterialModelImpl _$$ProductionMaterialModelImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductionMaterialModelImpl(
      storageItemId: json['storageItemId'] as String?,
      requiredQuantity: (json['requiredQuantity'] as num?)?.toDouble(),
      issuedQuantity: (json['issuedQuantity'] as num?)?.toDouble(),
      materialName: json['materialName'] as String?,
      materialUnit:
          $enumDecodeNullable(_$UnitTypeEnumMap, json['materialUnit']),
    );

Map<String, dynamic> _$$ProductionMaterialModelImplToJson(
        _$ProductionMaterialModelImpl instance) =>
    <String, dynamic>{
      if (instance.storageItemId case final value?) 'storageItemId': value,
      if (instance.requiredQuantity case final value?)
        'requiredQuantity': value,
      if (instance.issuedQuantity case final value?) 'issuedQuantity': value,
      if (instance.materialName case final value?) 'materialName': value,
      if (_$UnitTypeEnumMap[instance.materialUnit] case final value?)
        'materialUnit': value,
    };

const _$UnitTypeEnumMap = {
  UnitType.kg: 'kg',
  UnitType.pcs: 'pcs',
  UnitType.m: 'm',
  UnitType.m2: 'm2',
  UnitType.m3: 'm3',
  UnitType.l: 'l',
};
