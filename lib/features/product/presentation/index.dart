import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/core/navigation/index.gr.dart';
import 'package:sphere/features/product/data/models/product.dart';
import 'package:sphere/features/product/data/models/types.dart';
import 'package:sphere/features/product/data/repositories/index.dart';
import 'package:sphere/features/product/presentation/edit.dart';
import 'package:sphere/features/product/presentation/edit_operations.dart';
import 'package:sphere/features/product/presentation/edit_parameters/edit_parameters.dart';
import 'package:sphere/features/product/presentation/view_workers.dart';
import 'package:sphere/features/user/presentation/users_card.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/card/detailed.dart';
import 'package:sphere/shared/widgets/containers/card/techno.dart';
import 'package:sphere/shared/widgets/containers/wrapper/index.dart';
import 'package:sphere/shared/widgets/interactive/app_bar_features.dart';
import 'package:sphere/shared/widgets/interactive/custom_drawer.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';
import 'package:sphere/shared/widgets/overlay/app_bar/index.dart';
import 'package:sphere/shared/widgets/utility/info_list.dart';
import 'package:sphere/shared/widgets/utility/skeleton.dart';

@RoutePage()
class ProductScreen extends StatefulWidget {
  const ProductScreen({
    super.key,
    @PathParam('id') this.id,
    @PathParam('productId') this.productId,
  });

  final String? id;
  final String? productId;

  @override
  State<ProductScreen> createState() => _ProductScreenState();
}

class _ProductScreenState extends State<ProductScreen>
    with TickerProviderStateMixin {
  ProductModel _product = ProductModel();
  bool _isLoading = false;

  void _loadData() async {
    setState(() {
      _isLoading = true;
    });

    final result = await ProductRepository.view(widget.productId ?? '');
    // await Future.delayed(const Duration(milliseconds: 20000));

    setState(() {
      _product = result?.data ?? ProductModel();
      _isLoading = false;
    });
  }

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  Widget build(BuildContext context) {
    // final isMobile = MediaQuery.of(context).size.width < 600;

    return Wrapper(
      appBar: CustomAppBar(
        title: _product.name ?? 'NAME_OF_PRODUCT',
        isLoading: _isLoading,
        height: 48.0,
        rightPart: CustomAppBarFeatures.getPopupMenu(
          context: context,
          children: [
            if (_product.type != ProductType.materials)
              CustomDropdownMenuItem(
                // disabled: true,
                icon: Assets.icons.edit,
                text: 'Редактировать',
                onTap: () {
                  CustomDrawer.instance.show(
                    context: context,
                    vsync: this,
                    child: ProductEdit(
                      product: _product,
                      onSave: (product) {
                        setState(() {
                          _product = product;
                        });
                        // Не закрываем drawer здесь, это делается в ProductEdit
                      },
                    ),
                  );
                },
              ),
            CustomDropdownMenuItem(
              // disabled: true,
              icon: Assets.icons.edit,
              text: 'Редактировать параметры',
              onTap: () {
                CustomDropdownMenu.instance.hide();
                CustomDrawer.instance.show(
                  context: context,
                  vsync: this,
                  child: ProductEditParameters(
                    product: _product,
                    onSave: (product) {
                      setState(() {
                        _product = product;
                      });
                      CustomDropdownMenu.instance.hide();
                      CustomDrawer.instance.hide();
                    },
                  ),
                );
              },
            ),
            CustomDropdownMenuItem(
              disabled: true,
              icon: Assets.icons.description,
              text: 'Согласовать',
              onTap: () {},
            ),
            CustomDropdownMenuItem(
              disabled: true,
              onTap: () {},
              icon: Assets.icons.notifications,
              text: 'Подписаться на изменения',
            ),
          ],
        ),
      ),
      body: Row(children: [
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(12.0),
            itemCount: _getBodyItemCount(),
            itemBuilder: (context, index) {
              switch (index) {
                case 0:
                  if (_product.type == ProductType.assembly) {
                    return ConstrainedBox(
                      constraints: const BoxConstraints(maxHeight: 200),
                      child: Row(children: [
                        Expanded(
                          child: DetailedCard(
                            isLoading: _isLoading,
                            onTap: () {
                              context.router.push(
                                StructureRoute(
                                  id: widget.productId,
                                  projectId: widget.id,
                                  isProduct: true,
                                ),
                              );
                            },
                            title: 'Структура',
                            description:
                                'Структура изделия: "${_product.name}"',
                            icon: Assets.icons.widgets,
                            count: _product.allChildrenCount,
                          ),
                        ),
                      ]),
                    );
                  }
                  break;
                case 1:
                  return const SizedBox(height: 12.0);
                case 2:
                  return UsersCard(
                    onTap: () {
                      CustomDrawer.instance.show(
                        context: context,
                        vsync: this,
                        child: ProductViewWorkersDrawer(
                          productId: widget.productId,
                        ),
                      );
                    },
                    isLoading: _isLoading,
                  );
                case 3:
                  return const SizedBox(height: 12.0);
                case 4:
                  return TechnologicalMapCard(
                    isLoading: _isLoading,
                    onTap: () {
                      CustomDrawer.instance.show(
                        context: context,
                        vsync: this,
                        child: ProductEditOperationsBody(
                          product: _product,
                          refresher: _loadData,
                        ),
                      );
                    },
                    operations: _product.operations ?? [],
                  );
              }
              return const SizedBox.shrink();
            },
          ),
        ),
        SizedBox(
          width: MediaQuery.of(context).size.width / 3.00,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 12.0),
              Row(
                children: [
                  Expanded(
                    child: Skeleton(
                      isEnabled: _isLoading,
                      child: Text(
                        _product.name ?? 'NAME_OF_PRODUCT',
                        style: Fonts.titleLarge,
                      ),
                    ),
                  ),
                ],
              ),
              if (_isLoading) const SizedBox(height: 8.0),
              Row(
                children: [
                  Expanded(
                    child: Skeleton(
                      isEnabled: _isLoading,
                      child: Text(
                        '*Статус*',
                        // _product.status?.getName() ?? 'STATUS_OF_PRODUCT',
                        style: Fonts.bodyMedium.merge(
                          const TextStyle(color: AppColors.lightSecondary),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              if (_isLoading && _product.description != null)
                const SizedBox(height: 8.0),
              if (_product.description != null)
                Row(
                  children: [
                    Expanded(
                      child: Skeleton(
                        isEnabled: _isLoading,
                        child: Text(
                          _product.description ?? 'DESCRIPTION_OF_PRODUCT',
                          style: Fonts.bodySmall.merge(
                            const TextStyle(color: AppColors.medium),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              const SizedBox(height: 24.0),
              InfoList(isLoading: _isLoading, product: _product),
              const SizedBox(height: 24.0),
              const Divider(),
              const SizedBox(height: 24.0),
              // Row(mainAxisAlignment: MainAxisAlignment.spaceAround, children: [
              //   InfoBlock(
              //     icon: Assets.icons.weight,
              //     text: '${_product.parameters?.mass ?? '?'} кг',
              //   ),
              //   InfoBlock(
              //     icon: Assets.icons.fitHeigth,
              //     text: '${_product.parameters?.length ?? '?'} мм',
              //   ),
              //   InfoBlock(
              //     icon: Assets.icons.fitWidth,
              //     text: '${_product.parameters?.width ?? '?'} мм',
              //   ),
              // ]),
            ],
          ),
        ),
        const SizedBox(width: 12.0),
      ]),
    );
  }

  /// Calculate total item count for ListView.builder
  int _getBodyItemCount() {
    int count = 5; // Base items

    // Adjust for assembly type
    if (_product.type != ProductType.assembly) {
      count -= 1; // Remove structure card
    }

    return count;
  }
}
