part of 'bloc.dart';

@freezed
class ProductionState with _$ProductionState {
  const ProductionState._();
  
  const factory ProductionState({
    // Data
    @Default([]) List<ProductionItemModel> items,
    @Default([]) List<ProductionTaskModel> tasks,
    ProductionTaskModel? selectedTaskDetails,
    
    // UI State
    @Default(ProductionView.items) ProductionView currentView,
    @Default({}) Set<String> selectedItemIds,
    
    // Filters
    String? projectId,
    ProductionTaskStatus? statusFilter,
    DateTime? dateFromFilter,
    DateTime? dateToFilter,
    
    // Loading States
    @Default(false) bool isLoadingItems,
    @Default(false) bool isLoadingTasks,
    @Default(false) bool isLoadingTaskDetails,
    @Default(false) bool isCreatingTask,
    @Default(false) bool isCancellingTask,
    @Default(false) bool isUpdatingStatus,
    
    // Error States
    String? itemsError,
    String? tasksError,
    String? taskDetailsError,
    String? createTaskError,
    String? cancelTaskError,
    String? updateStatusError,
    
    // Success Messages
    String? successMessage,
  }) = _ProductionState;
}
